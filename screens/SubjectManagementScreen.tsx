import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  ArrowLeft,
  Plus,
  Search,
  Edit3,
  Trash2,
  Palette,
  Users,
  Clock,
  TrendingUp,
  AlertCircle,
} from 'lucide-react-native';
import { Subject } from '@/types/app';
import { useSubjectStore } from '@/stores/subjectStore';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import SubjectManager from '@/components/SubjectManager';
import { getContrastingTextColor } from '@/constants/subjectColors';

export default function SubjectManagementScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  const {
    subjects,
    isLoading,
    error,
    fetchSubjects,
    deleteSubject,
  } = useSubjectStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);

  useEffect(() => {
    if (user) {
      fetchSubjects(user.id);
    }
  }, [user]);

  const handleRefresh = async () => {
    if (!user) return;
    
    setRefreshing(true);
    try {
      await fetchSubjects(user.id, true);
    } catch (error) {
      console.error('Error refreshing subjects:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleDeleteSubject = (subject: Subject) => {
    Alert.alert(
      'Delete Subject',
      `Are you sure you want to delete "${subject.name}"? This will also remove all associated study sessions.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteSubject(subject.id);
              Alert.alert('Success', 'Subject deleted successfully');
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to delete subject');
            }
          },
        },
      ]
    );
  };

  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderSubjectCard = (subject: Subject) => {
    const textColor = getContrastingTextColor(subject.color);
    
    return (
      <View key={subject.id} style={styles.subjectCard}>
        <LinearGradient
          colors={[subject.color, subject.color + '80']}
          style={styles.subjectGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.subjectHeader}>
            <Text style={[styles.subjectName, { color: textColor }]} numberOfLines={1}>
              {subject.name}
            </Text>
            <View style={styles.subjectActions}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
                onPress={() => {
                  // Navigate to edit subject
                  setSelectedSubject(subject);
                }}
              >
                <Edit3 size={16} color={textColor} />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
                onPress={() => handleDeleteSubject(subject)}
              >
                <Trash2 size={16} color={textColor} />
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={styles.subjectStats}>
            <View style={styles.statItem}>
              <Clock size={14} color={textColor} />
              <Text style={[styles.statText, { color: textColor }]}>
                0h 0m
              </Text>
            </View>
            <View style={styles.statItem}>
              <Users size={14} color={textColor} />
              <Text style={[styles.statText, { color: textColor }]}>
                0 sessions
              </Text>
            </View>
          </View>
          
          <Text style={[styles.subjectDate, { color: textColor + '80' }]}>
            Created {subject.createdAt.toLocaleDateString()}
          </Text>
        </LinearGradient>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: theme.colors.ui.border }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color={theme.colors.text.primary} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text.primary }]}>
          Manage Subjects
        </Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.background.secondary }]}>
        <Search size={20} color={theme.colors.text.tertiary} />
        <TextInput
          style={[styles.searchInput, { color: theme.colors.text.primary }]}
          placeholder="Search subjects..."
          placeholderTextColor={theme.colors.text.tertiary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Subject Manager Component */}
      <View style={styles.managerSection}>
        <SubjectManager
          selectedSubject={selectedSubject}
          onSelectSubject={setSelectedSubject}
          showManageButton={false}
        />
      </View>

      {/* Subjects List */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.accent.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {isLoading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.accent.primary} />
            <Text style={[styles.loadingText, { color: theme.colors.text.secondary }]}>
              Loading subjects...
            </Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <AlertCircle size={48} color={theme.colors.status.error} />
            <Text style={[styles.errorTitle, { color: theme.colors.status.error }]}>
              Error Loading Subjects
            </Text>
            <Text style={[styles.errorMessage, { color: theme.colors.text.secondary }]}>
              {error}
            </Text>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: theme.colors.accent.primary }]}
              onPress={handleRefresh}
            >
              <Text style={[styles.retryButtonText, { color: theme.colors.text.inverse }]}>
                Try Again
              </Text>
            </TouchableOpacity>
          </View>
        ) : filteredSubjects.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Palette size={48} color={theme.colors.text.tertiary} />
            <Text style={[styles.emptyTitle, { color: theme.colors.text.primary }]}>
              {searchQuery ? 'No subjects found' : 'No subjects yet'}
            </Text>
            <Text style={[styles.emptyMessage, { color: theme.colors.text.secondary }]}>
              {searchQuery 
                ? 'Try adjusting your search terms'
                : 'Create your first subject to start organizing your study sessions'
              }
            </Text>
          </View>
        ) : (
          <View style={styles.subjectsList}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Your Subjects ({filteredSubjects.length})
            </Text>
            {filteredSubjects.map(renderSubjectCard)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginLeft: 16,
  },
  headerSpacer: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginVertical: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  managerSection: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  retryButton: {
    marginTop: 20,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyMessage: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  subjectsList: {
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  subjectCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  subjectGradient: {
    padding: 20,
  },
  subjectHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  subjectName: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  subjectActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  subjectStats: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 8,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 14,
    fontWeight: '500',
  },
  subjectDate: {
    fontSize: 12,
    fontWeight: '400',
  },
});
