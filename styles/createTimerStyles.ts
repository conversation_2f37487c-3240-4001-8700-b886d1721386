import { StyleSheet, Dimensions } from 'react-native';
import { Theme } from '@/types/theme';

const { width, height } = Dimensions.get('window');

// Base dimensions from the design
const baseWidth = 390;
const baseHeight = 844;

const scale = (size: number) => (width / baseWidth) * size;
const verticalScale = (size: number) => (height / baseHeight) * size;
const moderateScale = (size: number, factor = 0.5) =>
  size + (scale(size) - size) * factor;

export const createTimerStyles = (theme: Theme) => {
  const { colors, spacing, borderRadius } = theme;

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    scrollContent: {
      paddingBottom: verticalScale(spacing.xxl),
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: scale(spacing.lg),
      paddingTop: verticalScale(60),
      paddingBottom: verticalScale(spacing.md),
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: scale(spacing.md),
    },
    settingsButton: {
      width: scale(40),
      height: scale(40),
      borderRadius: scale(20),
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.card,
    },
    subjectSection: {
      paddingHorizontal: scale(spacing.lg),
      marginVertical: verticalScale(spacing.lg),
    },
    statsSection: {
      paddingHorizontal: scale(spacing.lg),
      marginBottom: verticalScale(spacing.lg),
    },
    statsGrid: {
      flexDirection: 'row',
      gap: scale(spacing.md),
    },
    statCard: {
      flex: 1,
      backgroundColor: colors.background.card,
      borderRadius: moderateScale(borderRadius.lg),
      padding: moderateScale(spacing.md),
      alignItems: 'center',
      gap: verticalScale(spacing.xs),
    },
    statValue: {
      fontSize: moderateScale(24),
      fontWeight: '600',
      color: colors.text.primary,
    },
    statLabel: {
      fontSize: moderateScale(12),
      fontWeight: '500',
      color: colors.text.secondary,
      textTransform: 'uppercase',
    },
    timerSection: {
      alignItems: 'center',
      marginVertical: verticalScale(spacing.xl),
      paddingHorizontal: scale(spacing.lg),
    },
    timerCard: {
      width: '100%',
      borderRadius: moderateScale(borderRadius.xl),
      padding: moderateScale(spacing.xl),
      marginBottom: verticalScale(spacing.xl),
      backgroundColor: colors.background.card,
    },
    timerHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: verticalScale(spacing.lg),
      gap: scale(spacing.md),
    },
    phaseIndicator: {
      width: scale(48),
      height: scale(48),
      borderRadius: scale(borderRadius.full),
      justifyContent: 'center',
      alignItems: 'center',
    },
    phaseIcon: {
      fontSize: moderateScale(24),
    },
    timerInfo: {
      flex: 1,
    },
    timerLabel: {
      fontSize: moderateScale(22),
      fontWeight: '600',
      color: colors.text.primary,
    },
    cycleText: {
      fontSize: moderateScale(14),
      fontWeight: '500',
      color: colors.text.secondary,
    },
    timerDisplay: {
      alignItems: 'center',
      marginVertical: verticalScale(spacing.lg),
    },
    timerText: {
      fontSize: moderateScale(width * 0.18, 0.8),
      fontWeight: '800',
      color: colors.text.primary,
      fontFamily: 'SF Pro Display',
      textAlign: 'center',
      letterSpacing: -2,
    },
    progressContainer: {
      width: '100%',
      alignItems: 'center',
      marginTop: verticalScale(spacing.md),
    },
    progressTrack: {
      width: '100%',
      height: verticalScale(6),
      borderRadius: moderateScale(3),
      backgroundColor: colors.background.secondary,
    },
    progressFill: {
      height: '100%',
      borderRadius: moderateScale(3),
    },
    timerControls: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      width: '100%',
      marginTop: verticalScale(spacing.md),
    },
    controlButton: {
      width: scale(72),
      height: scale(72),
      borderRadius: scale(36),
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.card,
    },
    playButton: {
      width: scale(80),
      height: scale(80),
      borderRadius: scale(40),
      backgroundColor: colors.accent.primary,
      transform: [{ scale: 1.1 }],
    },
    section: {
      paddingHorizontal: scale(spacing.lg),
      marginVertical: verticalScale(spacing.lg),
    },
    blockingSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: verticalScale(spacing.md),
    },
    sectionTitle: {
      fontSize: moderateScale(24),
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: verticalScale(spacing.md),
    },
    soundGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: scale(spacing.md),
    },
    soundCard: {
      width: (width - scale(spacing.lg * 2) - scale(spacing.md)) / 2,
      backgroundColor: colors.background.card,
      borderRadius: moderateScale(borderRadius.lg),
      padding: moderateScale(spacing.md),
      alignItems: 'center',
      gap: verticalScale(spacing.sm),
    },
    soundIcon: {
      fontSize: moderateScale(32),
    },
    soundName: {
      fontSize: moderateScale(15),
      fontWeight: '400',
      color: colors.text.primary,
    },
    activeBadge: {
      position: 'absolute',
      top: scale(8),
      right: scale(8),
      width: scale(24),
      height: scale(24),
      borderRadius: scale(12),
      justifyContent: 'center',
      alignItems: 'center',
    },
    blockingCard: {
      backgroundColor: colors.background.card,
      borderRadius: moderateScale(borderRadius.lg),
      padding: moderateScale(spacing.lg),
    },
    blockingHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: scale(spacing.md),
      marginBottom: verticalScale(spacing.md),
    },
    blockingTitle: {
      fontSize: moderateScale(20),
      fontWeight: '600',
      color: colors.text.primary,
      flex: 1,
    },
    statusBadge: {
      paddingHorizontal: scale(spacing.sm),
      paddingVertical: verticalScale(spacing.xs),
      borderRadius: moderateScale(borderRadius.sm),
    },
    statusText: {
      fontSize: moderateScale(12),
      fontWeight: '600',
      color: colors.text.primary,
    },
    blockingDescription: {
      fontSize: moderateScale(16),
      fontWeight: '400',
      color: colors.text.secondary,
      lineHeight: verticalScale(22),
      marginBottom: verticalScale(spacing.md),
    },
    manualBlockingButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: moderateScale(borderRadius.md),
      paddingVertical: verticalScale(spacing.md),
      backgroundColor: colors.background.secondary,
    },
    manualBlockingButtonText: {
      fontSize: moderateScale(15),
      fontWeight: '600',
      marginLeft: scale(spacing.sm),
    },
    progressCard: {
      backgroundColor: colors.background.card,
      borderRadius: moderateScale(borderRadius.lg),
      padding: moderateScale(spacing.lg),
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    statItem: {
      alignItems: 'center',
      gap: verticalScale(spacing.xs),
    },
    tipCard: {
      backgroundColor: colors.background.card,
      borderRadius: moderateScale(borderRadius.lg),
      padding: moderateScale(spacing.lg),
    },
    tipTitle: {
      fontSize: moderateScale(20),
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: verticalScale(spacing.sm),
    },
    tipText: {
      fontSize: moderateScale(16),
      fontWeight: '400',
      color: colors.text.secondary,
      lineHeight: verticalScale(22),
    },
  });
};
