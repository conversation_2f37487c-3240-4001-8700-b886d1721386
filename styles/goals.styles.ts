import { StyleSheet, Dimensions } from 'react-native';
import { Theme } from '@/types/theme';

const { width: screenWidth } = Dimensions.get('window');
const scale = (size: number) => (screenWidth / 375) * size; // Base screen width for scaling
const horizontalPadding = scale(24); // Example: scale a base padding of 24
const verticalPadding = scale(20); // Example: scale a base padding of 20

export const createGoalsStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  headerGradient: {
    paddingTop: scale(60),
    paddingBottom: verticalPadding,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: horizontalPadding,
  },
  subtitle: {
    fontSize: scale(14),
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    marginTop: scale(4),
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(12),
  },
  statsButton: {
    backgroundColor: theme.colors.background.secondary,
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.md,
  },
  filterButton: {
    backgroundColor: theme.colors.background.secondary,
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.md,
  },
  addButton: {
    width: scale(44),
    height: scale(44),
    backgroundColor: theme.colors.accent.primary,
    borderRadius: scale(22),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.accent.primary,
    shadowOffset: { width: 0, height: scale(4) },
    shadowOpacity: 0.3,
    shadowRadius: scale(8),
    elevation: scale(6),
  },
  searchContainer: {
    paddingHorizontal: horizontalPadding,
    paddingTop: scale(16),
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: scale(12),
    paddingHorizontal: scale(16),
    paddingVertical: scale(12),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2) },
    shadowOpacity: 0.1,
    shadowRadius: scale(4),
    elevation: scale(3),
  },
  searchInput: {
    flex: 1,
    marginLeft: scale(12),
    fontSize: scale(16),
    color: theme.colors.text.primary,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: horizontalPadding,
    paddingTop: scale(16),
    gap: scale(12),
  },
  statNumber: {
    fontSize: scale(24),
    fontWeight: 'bold',
    color: theme.colors.text.primary,
  },
  filterTabs: {
    paddingTop: scale(16),
  },
  tabsContainer: {
    paddingHorizontal: horizontalPadding,
  },
  filterTab: {
    paddingHorizontal: scale(20),
    paddingVertical: scale(8),
    marginRight: scale(12),
    borderRadius: scale(20),
    backgroundColor: theme.colors.background.secondary,
    borderWidth: scale(1),
    borderColor: theme.colors.ui.border,
  },
  activeFilterTab: {
    backgroundColor: theme.colors.accent.primary,
    borderColor: theme.colors.accent.primary,
  },
  filterTabText: {
    fontSize: scale(14),
    color: theme.colors.text.secondary,
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: theme.colors.text.inverse,
  },
  categoryFilter: {
    paddingTop: scale(12),
    paddingHorizontal: horizontalPadding,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    paddingVertical: scale(8),
    marginRight: scale(12),
    borderRadius: scale(20),
    backgroundColor: '#FFFFFF',
    borderWidth: scale(1),
    borderColor: '#E5E7EB',
  },
  activeCategoryChip: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  categoryChipText: {
    fontSize: scale(14),
    color: '#6B7280',
    fontWeight: '500',
  },
  activeCategoryChipText: {
    color: '#6366F1',
  },
  categoryDot: {
    width: scale(8),
    height: scale(8),
    borderRadius: scale(4),
    marginRight: scale(8),
  },
  taskListContainer: {
    flex: 1,
    paddingHorizontal: horizontalPadding,
    paddingTop: scale(16),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: scale(40),
  },
  loadingText: {
    fontSize: scale(16),
    color: '#6B7280',
  },
  // Task Card Styles
  taskCard: {
    backgroundColor: theme.colors.background.card,
    borderRadius: scale(16),
    padding: scale(16),
    marginBottom: scale(12),
    ...theme.shadows.md,
  },
  overdueCard: {
    borderLeftWidth: scale(4),
    borderLeftColor: theme.colors.status.error,
  },
  completedCard: {
    opacity: 0.7,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: scale(12),
  },
  statusButton: {
    marginRight: scale(12),
    marginTop: scale(2),
  },
  taskInfo: {
    flex: 1,
  },
  taskTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: scale(4),
  },
  taskTitle: {
    fontSize: scale(16),
    fontWeight: '600',
    color: theme.colors.text.primary,
    flex: 1,
  },
  completedTitle: {
    textDecorationLine: 'line-through',
    color: '#6B7280',
  },
  milestoneTag: {
    marginLeft: scale(8),
  },
  taskDescription: {
    fontSize: scale(14),
    color: '#6B7280',
    marginBottom: scale(8),
    lineHeight: scale(20),
  },
  completedDescription: {
    color: '#9CA3AF',
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: scale(8),
  },
  tag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: scale(8),
    paddingVertical: scale(4),
    borderRadius: scale(12),
    marginRight: scale(6),
  },
  tagText: {
    fontSize: scale(12),
    color: '#6B7280',
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: scale(12),
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
  subjectTag: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: scale(8),
  },
  subjectDot: {
    width: scale(8),
    height: scale(8),
    borderRadius: scale(4),
    marginRight: scale(6),
  },
  subjectName: {
    fontSize: scale(12),
    color: '#6B7280',
    fontWeight: '500',
  },
  editButton: {
    padding: scale(4),
  },
  progressContainer: {
    marginBottom: scale(12),
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: scale(8),
  },
  progressLabel: {
    fontSize: scale(12),
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  progressBadge: {
    backgroundColor: '#F3F4F6',
    borderRadius: scale(8),
    paddingHorizontal: scale(6),
    paddingVertical: scale(2),
  },
  progressBar: {
    height: scale(6),
    backgroundColor: '#F3F4F6',
    borderRadius: scale(3),
    position: 'relative',
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: scale(3),
  },
  progressText: {
    fontSize: scale(10),
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  completionIndicator: {
    position: 'absolute',
    right: scale(2),
    top: scale(-3),
    backgroundColor: '#FFFFFF',
    borderRadius: scale(8),
    padding: scale(1),
  },
  milestoneIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(4),
    marginTop: scale(6),
  },
  milestoneText: {
    fontSize: scale(11),
    fontFamily: 'Inter-Medium',
    color: theme.colors.status.warning,
  },
  sectionContainer: {
    marginTop: scale(20),
    paddingHorizontal: horizontalPadding,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    backgroundColor: theme.colors.background.card,
    borderRadius: scale(12),
    marginBottom: scale(12),
    ...theme.shadows.sm,
  },
  sectionTitle: {
    fontSize: scale(16),
    fontWeight: '600',
    color: theme.colors.text.primary,
    fontFamily: 'Inter-SemiBold',
  },
});

// Legacy export for backward compatibility
export const goalsStyles = createGoalsStyles;
