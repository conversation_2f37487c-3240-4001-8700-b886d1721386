import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { getNetworkErrorMessage, isNetworkError } from '@/utils/networkUtils';
import { LinearGradient } from 'expo-linear-gradient';
import { Mail, Lock, Eye, EyeOff, ArrowRight } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import IsotopeLogo from '@/components/IsotopeLogo';
import { Button, Input } from '@/components/ui';
import NetworkStatus from '@/components/NetworkStatus';

export default function SignInScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { signIn, user, isOnline } = useAuth();
  const { theme } = useTheme();
  const router = useRouter();

  // Redirect to main app if user is already authenticated
  useEffect(() => {
    if (user) {
      router.replace('/(tabs)');
    }
  }, [user, router]);

  const handleSignIn = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (!isOnline) {
      Alert.alert(
        'No Internet Connection',
        'Please check your internet connection and try again.'
      );
      return;
    }

    setLoading(true);
    try {
      const { error } = await signIn(email, password);

      if (error) {
        const errorMessage = isNetworkError(error)
          ? getNetworkErrorMessage(error)
          : error.message;
        Alert.alert('Sign In Failed', errorMessage);
      } else {
        // Success - the useEffect will handle navigation when user state updates
        console.log('Sign in successful');
      }
    } catch (error) {
      console.error('Sign in error:', error);
      const errorMessage = isNetworkError(error)
        ? getNetworkErrorMessage(error)
        : 'An unexpected error occurred. Please try again.';
      Alert.alert('Sign In Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    router.push('/(auth)/reset-password');
  };

  const handleSignUp = () => {
    router.push('/(auth)/sign-up');
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background.primary }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={styles.header}>
          <IsotopeLogo size="large" />
          <Text style={[styles.welcomeText, { color: theme.colors.text.primary }]}>Welcome back!</Text>
          <Text style={[styles.subtitleText, { color: theme.colors.text.secondary }]}>
            Sign in to continue your productivity journey
          </Text>

          {/* Network Status */}
          <View style={styles.networkStatusContainer}>
            <NetworkStatus />
          </View>
        </View>

        {/* Sign In Form */}
        <View style={styles.formContainer}>
          <LinearGradient
            colors={theme.mode === 'dark'
              ? [`${theme.colors.background.card}E6`, `${theme.colors.background.card}CC`]
              : ['rgba(255,255,255,0.9)', 'rgba(255,255,255,0.8)']
            }
            style={[styles.formCard, { backgroundColor: theme.colors.background.card }]}
          >
            <Text style={[styles.formTitle, { color: theme.colors.text.primary }]}>Sign In</Text>

            {/* Email Input */}
            <Input
              label="Email"
              placeholder="Enter your email address"
              value={email}
              onChangeText={setEmail}
              leftIcon={<Mail size={20} color={theme.colors.text.secondary} />}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              style={styles.inputContainer}
            />

            {/* Password Input */}
            <Input
              label="Password"
              placeholder="Enter your password"
              value={password}
              onChangeText={setPassword}
              leftIcon={<Lock size={20} color={theme.colors.text.secondary} />}
              rightIcon={
                showPassword ? (
                  <EyeOff size={20} color={theme.colors.text.secondary} />
                ) : (
                  <Eye size={20} color={theme.colors.text.secondary} />
                )
              }
              onRightIconPress={() => setShowPassword(!showPassword)}
              secureTextEntry={!showPassword}
              autoCapitalize="none"
              autoCorrect={false}
              style={styles.inputContainer}
            />

            {/* Forgot Password */}
            <TouchableOpacity
              style={styles.forgotPasswordButton}
              onPress={handleForgotPassword}
            >
              <Text style={[styles.forgotPasswordText, { color: theme.colors.accent.primary }]}>Forgot password?</Text>
            </TouchableOpacity>

            {/* Sign In Button */}
            <Button
              title={loading ? 'Signing In...' : 'Sign In'}
              onPress={handleSignIn}
              disabled={loading}
              loading={loading}
              variant="primary"
              size="lg"
              fullWidth
              gradient
              icon={!loading ? <ArrowRight size={20} color="#FFFFFF" /> : undefined}
              iconPosition="right"
              style={styles.signInButton}
            />
          </LinearGradient>
        </View>

        {/* Sign Up Link */}
        <View style={styles.signUpContainer}>
          <Text style={[styles.signUpText, { color: theme.colors.text.secondary }]}>Don't have an account? </Text>
          <TouchableOpacity onPress={handleSignUp}>
            <Text style={[styles.signUpLink, { color: theme.colors.accent.primary }]}>Sign up</Text>
          </TouchableOpacity>
        </View>

        {/* Features Preview */}
        <View style={[styles.featuresContainer, { 
          backgroundColor: theme.colors.background.secondary,
          borderColor: theme.colors.ui?.border || (theme.mode === 'dark' ? '#333' : '#E5E7EB')
        }]}>
          <Text style={[styles.featuresTitle, { color: theme.colors.text.primary }]}>What you'll get:</Text>
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <Text style={styles.featureIcon}>⏱️</Text>
              <Text style={[styles.featureText, { color: theme.colors.text.secondary }]}>Smart timer & focus sessions</Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={styles.featureIcon}>📊</Text>
              <Text style={[styles.featureText, { color: theme.colors.text.secondary }]}>Detailed analytics & progress tracking</Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={styles.featureIcon}>🛡️</Text>
              <Text style={[styles.featureText, { color: theme.colors.text.secondary }]}>Distraction blocking & focus mode</Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={styles.featureIcon}>☁️</Text>
              <Text style={[styles.featureText, { color: theme.colors.text.secondary }]}>Cloud sync across all devices</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be applied inline with theme
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  welcomeText: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    // color will be applied inline with theme
    marginTop: 24,
    marginBottom: 8,
  },
  subtitleText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    // color will be applied inline with theme
    textAlign: 'center',
    lineHeight: 24,
  },
  networkStatusContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  formContainer: {
    marginBottom: 32,
  },
  formCard: {
    borderRadius: 24,
    padding: 32,
    // backgroundColor and borderColor will be applied inline with theme
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 8,
  },
  formTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    // color will be applied inline with theme
    textAlign: 'center',
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 20,
  },
  forgotPasswordButton: {
    alignSelf: 'flex-end',
    marginBottom: 32,
  },
  forgotPasswordText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    // color will be applied inline with theme
  },
  signInButton: {
    marginTop: 8,
  },
  signUpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  signUpText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    // color will be applied inline with theme
  },
  signUpLink: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    // color will be applied inline with theme
  },
  featuresContainer: {
    borderRadius: 20,
    padding: 24,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 4,
  },
  featuresTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    // color will be applied inline with theme
    marginBottom: 16,
    textAlign: 'center',
  },
  featuresList: {
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureIcon: {
    fontSize: 20,
  },
  featureText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    // color will be applied inline with theme
    flex: 1,
  },
});
