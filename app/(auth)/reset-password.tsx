import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Mail, ArrowRight, ArrowLeft } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import IsotopeLogo from '@/components/IsotopeLogo';

export default function ResetPasswordScreen() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const { resetPassword } = useAuth();
  const { theme } = useTheme();
  const router = useRouter();

  const handleResetPassword = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    setLoading(true);
    const { error } = await resetPassword(email);
    setLoading(false);

    if (error) {
      Alert.alert('Reset Failed', error.message);
    } else {
      setEmailSent(true);
    }
  };

  const handleBackToSignIn = () => {
    router.push('/(auth)/sign-in');
  };

  if (emailSent) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <IsotopeLogo size="large" />
            <Text style={[styles.successTitle, { color: theme.colors.text.primary }]}>Check your email</Text>
            <Text style={[styles.successSubtitle, { color: theme.colors.text.secondary }]}>
              We've sent a password reset link to {email}
            </Text>
          </View>

          <View style={styles.formContainer}>
            <LinearGradient
              colors={theme.mode === 'dark'
                ? [`${theme.colors.background.card}E6`, `${theme.colors.background.card}CC`]
                : ['rgba(255,255,255,0.9)', 'rgba(255,255,255,0.8)']
              }
              style={[styles.formCard, { 
                backgroundColor: theme.colors.background.card,
                borderColor: theme.colors.ui?.border || (theme.mode === 'dark' ? '#333' : '#E5E7EB')
              }]}
            >
              <View style={styles.successIcon}>
                <Text style={styles.successEmoji}>📧</Text>
              </View>
              
              <Text style={[styles.instructionsTitle, { color: theme.colors.text.primary }]}>What's next?</Text>
              <Text style={[styles.instructionsText, { color: theme.colors.text.secondary }]}>
                1. Check your email inbox (and spam folder)
                {'\n'}2. Click the reset link in the email
                {'\n'}3. Create a new password
                {'\n'}4. Sign in with your new password
              </Text>

              <TouchableOpacity
                style={styles.backButton}
                onPress={handleBackToSignIn}
              >
                <LinearGradient
                  colors={theme.colors.gradients.primary}
                  style={styles.backGradient}
                >
                  <ArrowLeft size={20} color="#FFFFFF" />
                  <Text style={styles.backButtonText}>Back to Sign In</Text>
                </LinearGradient>
              </TouchableOpacity>
            </LinearGradient>
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: theme.colors.background.primary }]} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={styles.header}>
          <IsotopeLogo size="large" />
          <Text style={[styles.welcomeText, { color: theme.colors.text.primary }]}>Reset Password</Text>
          <Text style={[styles.subtitleText, { color: theme.colors.text.secondary }]}>
            Enter your email address and we'll send you a link to reset your password
          </Text>
        </View>

        {/* Reset Form */}
        <View style={styles.formContainer}>
          <LinearGradient
            colors={theme.mode === 'dark'
              ? [`${theme.colors.background.card}E6`, `${theme.colors.background.card}CC`]
              : ['rgba(255,255,255,0.9)', 'rgba(255,255,255,0.8)']
            }
            style={[styles.formCard, { 
              backgroundColor: theme.colors.background.card,
              borderColor: theme.colors.ui?.border || (theme.mode === 'dark' ? '#333' : '#E5E7EB')
            }]}
          >
            <Text style={[styles.formTitle, { color: theme.colors.text.primary }]}>Reset Password</Text>

            {/* Email Input */}
            <View style={styles.inputContainer}>
              <View style={[styles.inputWrapper, { backgroundColor: theme.colors.background.secondary, borderColor: theme.colors.ui?.border || (theme.mode === 'dark' ? '#333' : '#E5E7EB') }]}>
                <Mail size={20} color={theme.colors.text.secondary} style={styles.inputIcon} />
                <TextInput
                  style={[styles.textInput, { color: theme.colors.text.primary }]}
                  placeholder="Email address"
                  placeholderTextColor={theme.colors.text.secondary}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            </View>

            {/* Reset Button */}
            <TouchableOpacity
              style={styles.resetButton}
              onPress={handleResetPassword}
              disabled={loading}
            >
              <LinearGradient
                colors={theme.colors.gradients.primary}
                style={styles.resetGradient}
              >
                <Text style={styles.resetButtonText}>
                  {loading ? 'Sending...' : 'Send Reset Link'}
                </Text>
                {!loading && <ArrowRight size={20} color="#FFFFFF" />}
              </LinearGradient>
            </TouchableOpacity>
          </LinearGradient>
        </View>

        {/* Back to Sign In */}
        <View style={styles.backContainer}>
          <TouchableOpacity 
            style={styles.backToSignInButton}
            onPress={handleBackToSignIn}
          >
            <ArrowLeft size={16} color={theme.colors.accent.primary} />
            <Text style={[styles.backToSignInText, { color: theme.colors.accent.primary }]}>Back to Sign In</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  welcomeText: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    marginTop: 24,
    marginBottom: 8,
  },
  subtitleText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
  },
  successTitle: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    marginTop: 24,
    marginBottom: 8,
  },
  successSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
  },
  formContainer: {
    marginBottom: 32,
  },
  formCard: {
    borderRadius: 24,
    padding: 32,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 8,
  },
  formTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 32,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  inputIcon: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  resetButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#7C3AED',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  resetGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
  },
  resetButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  backContainer: {
    alignItems: 'center',
  },
  backToSignInButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  backToSignInText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  successIcon: {
    alignItems: 'center',
    marginBottom: 24,
  },
  successEmoji: {
    fontSize: 48,
  },
  instructionsTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    marginBottom: 16,
  },
  instructionsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 22,
    marginBottom: 32,
  },
  backButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  backGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});
