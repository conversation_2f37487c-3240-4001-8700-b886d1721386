import React from 'react';
import { View, StyleSheet } from 'react-native';
import { AnalyticsDashboard } from '@/components/analytics/AnalyticsDashboard';
import { useTheme } from '@/contexts/ThemeContext';

export default function AnalyticsScreen() {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
      <AnalyticsDashboard />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});


