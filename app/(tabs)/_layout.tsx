import { Tabs } from 'expo-router';
import { Timer, Target, ChartBar as BarChart3, Settings } from 'lucide-react-native';
import { Platform } from 'react-native';
import AuthGuard from '@/components/AuthGuard';
import { useTheme } from '@/contexts/ThemeContext';

export default function TabLayout() {
  const { theme } = useTheme();

  return (
    <AuthGuard>
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: theme.colors.ui.tabBar.background,
          borderTopWidth: 1,
          borderTopColor: theme.colors.ui.tabBar.border,
          paddingBottom: Platform.OS === 'ios' ? 30 : 10,
          paddingTop: 10,
          height: Platform.OS === 'ios' ? 90 : 70,
          ...theme.shadows.lg,
        },
        tabBarActiveTintColor: theme.colors.ui.tabBar.active,
        tabBarInactiveTintColor: theme.colors.ui.tabBar.inactive,
        tabBarLabelStyle: {
          fontFamily: 'Inter-Medium',
          fontSize: 12,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Timer',
          tabBarIcon: ({ size, color }) => (
            <Timer size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="goals"
        options={{
          title: 'Goals',
          tabBarIcon: ({ size, color }) => (
            <Target size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="analytics"
        options={{
          title: 'Analytics',
          tabBarIcon: ({ size, color }) => (
            <BarChart3 size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ size, color }) => (
            <Settings size={size} color={color} />
          ),
        }}
      />
    </Tabs>
    </AuthGuard>
  );
}