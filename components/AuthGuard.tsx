import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Redirect } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import IsotopeLogo from './IsotopeLogo';

interface AuthGuardProps {
  children: React.ReactNode;
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const { user, loading } = useAuth();
  const { theme } = useTheme();

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={[theme.colors.background.primary, theme.colors.background.tertiary]}
          style={styles.loadingGradient}
        >
          <IsotopeLogo size="large" />
          <View style={styles.loadingContent}>
            <ActivityIndicator size="large" color={theme.colors.accent.primary} />
            <Text style={[styles.loadingText, { color: theme.colors.text.secondary }]}>Loading...</Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  if (!user) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  return <>{children}</>;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
  },
  loadingGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  loadingContent: {
    alignItems: 'center',
    marginTop: 40,
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    // Color will be applied inline with theme
  },
});
