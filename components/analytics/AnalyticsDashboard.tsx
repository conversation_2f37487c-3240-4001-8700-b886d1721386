import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useTheme } from '@/contexts/ThemeContext';

// Tab Components
import { OverviewTab } from './tabs/OverviewTab';
import { DailyTab } from './tabs/DailyTab';
import { WeeklyTab } from './tabs/WeeklyTab';
import { MonthlyTab } from './tabs/MonthlyTab';
import { YearlyTab } from './tabs/YearlyTab';
import { AllTimeTab } from './tabs/AllTimeTab';
import { SubjectsTab } from './tabs/SubjectsTab';
import { TaskTypesTab } from './tabs/TaskTypesTab';
import AnalyticsHeader from './AnalyticsHeader';

interface AnalyticsTab {
  id: string;
  title: string;
  icon: keyof typeof MaterialIcons.glyphMap;
}

const ANALYTICS_TABS: AnalyticsTab[] = [
  { id: 'overview', title: 'Overview', icon: 'dashboard' },
  { id: 'daily', title: 'Daily', icon: 'today' },
  { id: 'weekly', title: 'Weekly', icon: 'view-week' },
  { id: 'monthly', title: 'Monthly', icon: 'calendar-month' },
  { id: 'yearly', title: 'Yearly', icon: 'calendar-today' },
  { id: 'all-time', title: 'All Time', icon: 'history' },
  { id: 'subjects', title: 'Subjects', icon: 'school' },
  { id: 'task-types', title: 'Task Types', icon: 'category' },
];

export function AnalyticsDashboard() {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const {
    loading,
    error,
    refreshAnalytics,
    quickStats,
    analytics,
    timeSeriesData,
    subjectChartData,
    taskTypeChartData,
    performanceMetrics,
    todayProgress,
    weekProgress,
    formatTime,
    getStreakMessage,
  } = useAnalytics();

  const renderTabContent = () => {
    const commonProps = {
      analytics,
      timeSeriesData,
      subjectChartData,
      taskTypeChartData,
      performanceMetrics,
      quickStats,
      todayProgress,
      weekProgress,
      formatTime,
      getStreakMessage,
      loading,
      error,
    };

    switch (activeTab) {
      case 'overview':
        return <OverviewTab {...commonProps} />;
      case 'daily':
        return <DailyTab {...commonProps} />;
      case 'weekly':
        return <WeeklyTab {...commonProps} />;
      case 'monthly':
        return <MonthlyTab {...commonProps} />;
      case 'yearly':
        return <YearlyTab {...commonProps} />;
      case 'all-time':
        return <AllTimeTab {...commonProps} />;
      case 'subjects':
        return <SubjectsTab {...commonProps} />;
      case 'task-types':
        return <TaskTypesTab {...commonProps} />;
      default:
        return <OverviewTab {...commonProps} />;
    }
  };



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
      {/* Header */}
      <AnalyticsHeader onRefresh={refreshAnalytics} loading={loading} />

      {/* Tab Navigation */}
      <View style={[styles.tabNavigationContainer, { backgroundColor: theme.colors.background.primary }]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.tabScrollView}
          contentContainerStyle={styles.tabContainer}
        >
          {ANALYTICS_TABS.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tab,
                { backgroundColor: theme.colors.background.secondary },
                activeTab === tab.id && [styles.tabActive, { backgroundColor: theme.colors.accent.primary }],
              ]}
              onPress={() => setActiveTab(tab.id)}
            >
              <MaterialIcons
                name={tab.icon}
                size={18}
                color={activeTab === tab.id ? theme.colors.text.inverse : theme.colors.text.secondary}
              />
              <Text
                style={[
                  styles.tabText,
                  { color: theme.colors.text.secondary },
                  activeTab === tab.id && [styles.tabTextActive, { color: theme.colors.text.inverse }]
                ]}
              >
                {tab.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Tab Content */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={refreshAnalytics}
            colors={[theme.colors.accent.primary]}
            tintColor={theme.colors.accent.primary}
          />
        }
      >
        {renderTabContent()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabNavigationContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  tabScrollView: {
    marginHorizontal: -20,
  },
  tabContainer: {
    paddingHorizontal: 20,
    gap: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    gap: 6,
  },
  tabActive: {
    // Active styles will be applied inline with theme colors
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  tabTextActive: {
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
});
