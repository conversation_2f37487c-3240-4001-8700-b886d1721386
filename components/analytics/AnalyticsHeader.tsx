import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import IsotopeLogo from '@/components/IsotopeLogo';
import { useTheme } from '@/contexts/ThemeContext';

const { width } = Dimensions.get('window');

interface AnalyticsHeaderProps {
  onRefresh: () => void;
  loading: boolean;
}

const scale = (size: number, screenWidth: number = width) => (screenWidth / 375) * size;

const AnalyticsHeader: React.FC<AnalyticsHeaderProps> = ({
  onRefresh,
  loading,
}) => {
  const { theme } = useTheme();
  const screenWidth = width;
  const localScale = (size: number) => scale(size, screenWidth);

  return (
    <LinearGradient
      colors={[theme.colors.background.primary, theme.colors.background.secondary, theme.colors.background.primary]}
      style={styles(screenWidth).headerGradient}
    >
      <View style={styles(screenWidth).header}>
        <View style={styles(screenWidth).headerLeft}>
          <IsotopeLogo size="medium" />
          <Text numberOfLines={1} style={[styles(screenWidth).subtitle, { color: theme.colors.text.secondary }]}>
            Study Analytics
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = (screenWidth: number) => {
  const localScale = (size: number) => scale(size, screenWidth);
  
  return StyleSheet.create({
    headerGradient: {
      paddingTop: localScale(60),
      paddingBottom: localScale(20),
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: localScale(24),
    },
    headerLeft: {
      flex: 1,
      marginRight: localScale(16),
      overflow: 'hidden',
    },
    subtitle: {
      fontSize: localScale(14),
      fontFamily: 'Inter-Regular',
      marginTop: localScale(4),
    },
  });
};

export default AnalyticsHeader;
