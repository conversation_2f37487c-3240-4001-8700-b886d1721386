import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, ChartDataPoint, TimeSeriesDataPoint } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';

const { width } = Dimensions.get('window');

interface AllTimeTabProps {
  analytics: Analytics | null;
  timeSeriesData: TimeSeriesDataPoint[];
  subjectChartData: ChartDataPoint[];
  taskTypeChartData: ChartDataPoint[];
  performanceMetrics: any;
  quickStats: any;
  todayProgress: any;
  weekProgress: any;
  formatTime: (seconds: number) => string;
  getStreakMessage: () => string;
  loading: boolean;
  error: string | null;
}

export function AllTimeTab({
  analytics,
  timeSeriesData,
  subjectChartData,
  taskTypeChartData,
  performanceMetrics,
  quickStats,
  todayProgress,
  weekProgress,
  formatTime,
  getStreakMessage,
  loading,
  error
}: AllTimeTabProps) {
  const { theme } = useTheme();

  const renderAllTimeStats = () => {
    if (!analytics) return null;

    const totalStudyTime = analytics.totalStudyTime || 0;
    const totalSessions = analytics.totalSessions || 0;
    const totalDays = analytics.totalActiveDays || 0;
    const averageSessionLength = totalSessions > 0 ? totalStudyTime / totalSessions : 0;
    const averageDailyTime = totalDays > 0 ? totalStudyTime / totalDays : 0;

    return (
      <View style={styles(theme).statsContainer}>
        <Text style={styles(theme).sectionTitle}>All-Time Statistics</Text>
        <View style={styles(theme).statsGrid}>
          <View style={styles(theme).statCard}>
            <MaterialIcons name="schedule" size={24} color={theme.colors.accent.primary} />
            <Text style={styles(theme).statValue}>{formatTime(totalStudyTime)}</Text>
            <Text style={styles(theme).statLabel}>Total Study Time</Text>
          </View>
          
          <View style={styles(theme).statCard}>
            <MaterialIcons name="play-circle-outline" size={24} color={theme.colors.accent.secondary} />
            <Text style={styles(theme).statValue}>{totalSessions.toLocaleString()}</Text>
            <Text style={styles(theme).statLabel}>Total Sessions</Text>
          </View>
          
          <View style={styles(theme).statCard}>
            <MaterialIcons name="calendar-today" size={24} color={theme.colors.status.success} />
            <Text style={styles(theme).statValue}>{totalDays}</Text>
            <Text style={styles(theme).statLabel}>Active Days</Text>
          </View>
          
          <View style={styles(theme).statCard}>
            <MaterialIcons name="trending-up" size={24} color={theme.colors.accent.tertiary} />
            <Text style={styles(theme).statValue}>{formatTime(averageSessionLength)}</Text>
            <Text style={styles(theme).statLabel}>Avg Session</Text>
          </View>
          
          <View style={styles(theme).statCard}>
            <MaterialIcons name="today" size={24} color={theme.colors.accent.primary} />
            <Text style={styles(theme).statValue}>{formatTime(averageDailyTime)}</Text>
            <Text style={styles(theme).statLabel}>Avg Daily</Text>
          </View>
          
          <View style={styles(theme).statCard}>
            <MaterialIcons name="local-fire-department" size={24} color={theme.colors.status.warning} />
            <Text style={styles(theme).statValue}>{analytics.currentStreak || 0}</Text>
            <Text style={styles(theme).statLabel}>Current Streak</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderTopSubjects = () => {
    if (!analytics?.subjectStats || analytics.subjectStats.length === 0) return null;

    const topSubjects = analytics.subjectStats.slice(0, 5);

    return (
      <View style={styles(theme).sectionContainer}>
        <Text style={styles(theme).sectionTitle}>Top Subjects (All Time)</Text>
        {topSubjects.map((subject, index) => (
          <View key={subject.subject} style={styles(theme).listItem}>
            <View style={styles(theme).listItemLeft}>
              <View style={[styles(theme).rankBadge, { backgroundColor: subject.color }]}>
                <Text style={styles(theme).rankText}>#{index + 1}</Text>
              </View>
              <View style={styles(theme).listItemInfo}>
                <Text style={styles(theme).listItemTitle}>{subject.subject}</Text>
                <Text style={styles(theme).listItemSubtitle}>
                  {subject.sessionCount} sessions • {((subject.totalDuration / analytics.totalStudyTime) * 100).toFixed(1)}% of total
                </Text>
              </View>
            </View>
            <Text style={styles(theme).listItemValue}>{formatTime(subject.totalDuration)}</Text>
          </View>
        ))}
      </View>
    );
  };

  const renderTopTaskTypes = () => {
    if (!analytics?.taskTypeStats || analytics.taskTypeStats.length === 0) return null;

    const topTaskTypes = analytics.taskTypeStats.slice(0, 5);

    return (
      <View style={styles(theme).sectionContainer}>
        <Text style={styles(theme).sectionTitle}>Top Task Types (All Time)</Text>
        {topTaskTypes.map((taskType, index) => {
          const colors = ['#6366F1', '#8B5CF6', '#EC4899', '#EF4444', '#F59E0B'];
          const color = colors[index % colors.length];
          
          return (
            <View key={taskType.taskType} style={styles(theme).listItem}>
              <View style={styles(theme).listItemLeft}>
                <View style={[styles(theme).rankBadge, { backgroundColor: `${color}20` }]}>
                  <Text style={[styles(theme).rankText, { color }]}>#{index + 1}</Text>
                </View>
                <View style={styles(theme).listItemInfo}>
                  <Text style={styles(theme).listItemTitle}>{taskType.taskType}</Text>
                  <Text style={styles(theme).listItemSubtitle}>
                    {taskType.sessionCount} sessions • {((taskType.totalDuration / analytics.totalStudyTime) * 100).toFixed(1)}% of total
                  </Text>
                </View>
              </View>
              <Text style={styles(theme).listItemValue}>{formatTime(taskType.totalDuration)}</Text>
            </View>
          );
        })}
      </View>
    );
  };

  const renderMilestones = () => {
    if (!analytics) return null;

    const milestones = [];
    const totalHours = Math.floor(analytics.totalStudyTime / 3600);
    
    // Add hour milestones
    if (totalHours >= 1000) milestones.push({ icon: 'emoji-events', text: '1000+ Hours Studied!', color: theme.colors.status.success });
    else if (totalHours >= 500) milestones.push({ icon: 'emoji-events', text: '500+ Hours Studied!', color: theme.colors.status.success });
    else if (totalHours >= 100) milestones.push({ icon: 'emoji-events', text: '100+ Hours Studied!', color: theme.colors.status.success });
    
    // Add session milestones
    if (analytics.totalSessions >= 1000) milestones.push({ icon: 'star', text: '1000+ Sessions!', color: theme.colors.accent.primary });
    else if (analytics.totalSessions >= 500) milestones.push({ icon: 'star', text: '500+ Sessions!', color: theme.colors.accent.primary });
    else if (analytics.totalSessions >= 100) milestones.push({ icon: 'star', text: '100+ Sessions!', color: theme.colors.accent.primary });

    // Add streak milestones
    if (analytics.longestStreak >= 30) milestones.push({ icon: 'local-fire-department', text: '30+ Day Streak!', color: theme.colors.status.warning });
    else if (analytics.longestStreak >= 7) milestones.push({ icon: 'local-fire-department', text: '7+ Day Streak!', color: theme.colors.status.warning });

    if (milestones.length === 0) return null;

    return (
      <View style={styles(theme).sectionContainer}>
        <Text style={styles(theme).sectionTitle}>Achievements</Text>
        <View style={styles(theme).milestonesGrid}>
          {milestones.map((milestone, index) => (
            <View key={index} style={styles(theme).milestoneCard}>
              <MaterialIcons name={milestone.icon as any} size={32} color={milestone.color} />
              <Text style={styles(theme).milestoneText}>{milestone.text}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles(theme).loadingContainer}>
        <MaterialIcons name="history" size={48} color={theme.colors.accent.primary} />
        <Text style={styles(theme).loadingText}>Loading all-time analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles(theme).errorContainer}>
        <MaterialIcons name="error-outline" size={48} color={theme.colors.status.error} />
        <Text style={styles(theme).errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles(theme).container} showsVerticalScrollIndicator={false}>
      {renderAllTimeStats()}
      {renderMilestones()}
      {renderTopSubjects()}
      {renderTopTaskTypes()}
    </ScrollView>
  );
}

const styles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  statsContainer: {
    margin: 20,
  },
  sectionContainer: {
    margin: 20,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    ...theme.shadows.sm,
  },
  listItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rankBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  rankText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text.inverse,
  },
  listItemInfo: {
    flex: 1,
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: 2,
  },
  listItemSubtitle: {
    fontSize: 12,
    color: theme.colors.text.secondary,
  },
  listItemValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.accent.primary,
  },
  milestonesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  milestoneCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  milestoneText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginTop: 8,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.status.error,
    marginTop: 12,
    textAlign: 'center',
  },
});
