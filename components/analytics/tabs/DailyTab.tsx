import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { Analytics, DailyStat } from '@/types/app';

const { width } = Dimensions.get('window');

interface DailyTabProps {
  analytics: Analytics | null;
  formatTime: (seconds: number) => string;
  loading: boolean;
  error: string | null;
}

export function DailyTab({ analytics, formatTime, loading, error }: DailyTabProps) {
  const { theme } = useTheme();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month'>('week');

  const getDailyStatsForPeriod = (): DailyStat[] => {
    if (!analytics?.dailyStats) return [];
    
    const now = new Date();
    const daysToShow = selectedPeriod === 'week' ? 7 : 30;
    const cutoffDate = new Date();
    cutoffDate.setDate(now.getDate() - daysToShow);
    
    return analytics.dailyStats
      .filter(stat => new Date(stat.date) >= cutoffDate)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  const renderPeriodSelector = () => {
    return (
      <View style={styles(theme).periodSelector}>
        <TouchableOpacity
          style={[
            styles(theme).periodButton,
            selectedPeriod === 'week' && styles(theme).periodButtonActive,
          ]}
          onPress={() => setSelectedPeriod('week')}
        >
          <Text
            style={[
              styles(theme).periodText,
              selectedPeriod === 'week' && styles(theme).periodTextActive,
            ]}
          >
            Last 7 Days
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles(theme).periodButton,
            selectedPeriod === 'month' && styles(theme).periodButtonActive,
          ]}
          onPress={() => setSelectedPeriod('month')}
        >
          <Text
            style={[
              styles(theme).periodText,
              selectedPeriod === 'month' && styles(theme).periodTextActive,
            ]}
          >
            Last 30 Days
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderBarChart = () => {
    const dailyStats = getDailyStatsForPeriod();
    if (dailyStats.length === 0) return null;

    const maxDuration = Math.max(...dailyStats.map(stat => stat.totalDuration));
    const chartHeight = 120;

    return (
      <View style={styles(theme).chartContainer}>
        <Text style={styles(theme).chartTitle}>Daily Study Time</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles(theme).barChart}>
            {dailyStats.reverse().map((stat, index) => {
              const height = maxDuration > 0 ? (stat.totalDuration / maxDuration) * chartHeight : 0;
              const date = new Date(stat.date);
              const isToday = date.toDateString() === new Date().toDateString();

              return (
                <View key={stat.date} style={styles(theme).barContainer}>
                  <View style={[styles(theme).barWrapper, { height: chartHeight }]}>
                    <LinearGradient
                      colors={stat.targetAchieved ? theme.colors.gradients.accent : theme.colors.gradients.primary}
                      style={[
                        styles(theme).bar,
                        {
                          height: Math.max(height, 4),
                          opacity: isToday ? 1 : 0.8
                        }
                      ]}
                    />
                  </View>
                  <Text style={[styles(theme).barLabel, isToday && styles(theme).barLabelToday]}>
                    {date.toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric'
                    })}
                  </Text>
                  <Text style={styles(theme).barValue}>
                    {formatTime(stat.totalDuration)}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={12} color={theme.colors.status.success} />
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderDailyList = () => {
    const dailyStats = getDailyStatsForPeriod();
    if (dailyStats.length === 0) return null;

    return (
      <View style={styles(theme).listContainer}>
        <Text style={styles(theme).listTitle}>Daily Breakdown</Text>
        {dailyStats.map((stat) => {
          const date = new Date(stat.date);
          const isToday = date.toDateString() === new Date().toDateString();
          const isYesterday = date.toDateString() === new Date(Date.now() - 86400000).toDateString();
          
          let dateLabel = date.toLocaleDateString('en-US', { 
            weekday: 'long', 
            month: 'short', 
            day: 'numeric' 
          });
          
          if (isToday) dateLabel = 'Today';
          else if (isYesterday) dateLabel = 'Yesterday';

          return (
            <View key={stat.date} style={styles(theme).dailyItem}>
              <View style={styles(theme).dailyHeader}>
                <View style={styles(theme).dailyDateContainer}>
                  <Text style={[styles(theme).dailyDate, isToday && styles(theme).dailyDateToday]}>
                    {dateLabel}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={16} color={theme.colors.status.success} />
                  )}
                </View>
                <Text style={styles(theme).dailyDuration}>
                  {formatTime(stat.totalDuration)}
                </Text>
              </View>
              
              {stat.sessionCount > 0 && (
                <View style={styles(theme).dailyDetails}>
                  <View style={styles(theme).dailyDetailItem}>
                    <MaterialIcons name="play-circle-outline" size={16} color={theme.colors.text.secondary} />
                    <Text style={styles(theme).dailyDetailText}>
                      {stat.sessionCount} session{stat.sessionCount !== 1 ? 's' : ''}
                    </Text>
                  </View>

                  {stat.completedPomodoros > 0 && (
                    <View style={styles(theme).dailyDetailItem}>
                      <MaterialIcons name="timer" size={16} color={theme.colors.text.secondary} />
                      <Text style={styles(theme).dailyDetailText}>
                        {stat.completedPomodoros} pomodoro{stat.completedPomodoros !== 1 ? 's' : ''}
                      </Text>
                    </View>
                  )}

                  {stat.averageProductivityRating > 0 && (
                    <View style={styles(theme).dailyDetailItem}>
                      <MaterialIcons name="star" size={16} color={theme.colors.status.warning} />
                      <Text style={styles(theme).dailyDetailText}>
                        {stat.averageProductivityRating.toFixed(1)} rating
                      </Text>
                    </View>
                  )}
                </View>
              )}
              
              {/* Subject breakdown */}
              {Object.keys(stat.subjectDurations).length > 0 && (
                <View style={styles(theme).subjectBreakdown}>
                  {Object.entries(stat.subjectDurations)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 3)
                    .map(([subject, duration]) => (
                      <View key={subject} style={styles(theme).subjectItem}>
                        <Text style={styles(theme).subjectName}>{subject}</Text>
                        <Text style={styles(theme).subjectDuration}>
                          {formatTime(duration)}
                        </Text>
                      </View>
                    ))}
                </View>
              )}
            </View>
          );
        })}
      </View>
    );
  };

  const renderSummaryStats = () => {
    const dailyStats = getDailyStatsForPeriod();
    if (dailyStats.length === 0) return null;

    const totalTime = dailyStats.reduce((sum, stat) => sum + stat.totalDuration, 0);
    const totalSessions = dailyStats.reduce((sum, stat) => sum + stat.sessionCount, 0);
    const daysWithStudy = dailyStats.filter(stat => stat.totalDuration > 0).length;
    const targetAchievedDays = dailyStats.filter(stat => stat.targetAchieved).length;

    return (
      <View style={styles(theme).summaryContainer}>
        <Text style={styles(theme).summaryTitle}>
          {selectedPeriod === 'week' ? 'Week' : 'Month'} Summary
        </Text>
        <View style={styles(theme).summaryGrid}>
          <View style={styles(theme).summaryCard}>
            <Text style={styles(theme).summaryValue}>{formatTime(totalTime)}</Text>
            <Text style={styles(theme).summaryLabel}>Total Time</Text>
          </View>
          <View style={styles(theme).summaryCard}>
            <Text style={styles(theme).summaryValue}>{totalSessions}</Text>
            <Text style={styles(theme).summaryLabel}>Sessions</Text>
          </View>
          <View style={styles(theme).summaryCard}>
            <Text style={styles(theme).summaryValue}>{daysWithStudy}</Text>
            <Text style={styles(theme).summaryLabel}>Active Days</Text>
          </View>
          <View style={styles(theme).summaryCard}>
            <Text style={styles(theme).summaryValue}>{targetAchievedDays}</Text>
            <Text style={styles(theme).summaryLabel}>Goals Met</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles(theme).loadingContainer}>
        <MaterialIcons name="today" size={48} color={theme.colors.accent.primary} />
        <Text style={styles(theme).loadingText}>Loading daily analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles(theme).errorContainer}>
        <MaterialIcons name="error-outline" size={48} color={theme.colors.status.error} />
        <Text style={styles(theme).errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles(theme).container} showsVerticalScrollIndicator={false}>
      {renderPeriodSelector()}
      {renderSummaryStats()}
      {renderBarChart()}
      {renderDailyList()}
    </ScrollView>
  );
}

const styles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  periodSelector: {
    flexDirection: 'row',
    margin: 20,
    backgroundColor: theme.colors.background.secondary,
    borderRadius: 12,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  periodButtonActive: {
    backgroundColor: theme.colors.accent.primary,
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  periodTextActive: {
    color: theme.colors.text.inverse,
  },
  summaryContainer: {
    margin: 20,
    marginTop: 0,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    ...theme.shadows.md,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: theme.colors.text.secondary,
  },
  chartContainer: {
    backgroundColor: theme.colors.background.card,
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    ...theme.shadows.md,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
    paddingBottom: 40,
  },
  barContainer: {
    alignItems: 'center',
    minWidth: 40,
  },
  barWrapper: {
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 24,
    borderRadius: 12,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 10,
    color: theme.colors.text.secondary,
    marginBottom: 2,
    textAlign: 'center',
  },
  barLabelToday: {
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  barValue: {
    fontSize: 9,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  listContainer: {
    margin: 20,
    marginTop: 0,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  dailyItem: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    ...theme.shadows.sm,
  },
  dailyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dailyDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dailyDate: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  dailyDateToday: {
    color: theme.colors.accent.primary,
    fontWeight: '600',
  },
  dailyDuration: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.accent.primary,
  },
  dailyDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 8,
  },
  dailyDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dailyDetailText: {
    fontSize: 12,
    color: theme.colors.text.secondary,
  },
  subjectBreakdown: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.ui.border,
    paddingTop: 8,
    gap: 4,
  },
  subjectItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subjectName: {
    fontSize: 12,
    color: theme.colors.text.secondary,
  },
  subjectDuration: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.status.error,
    marginTop: 12,
    textAlign: 'center',
  },
});
