import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, ChartDataPoint, TimeSeriesDataPoint } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';

const { width } = Dimensions.get('window');

interface OverviewTabProps {
  analytics: Analytics | null;
  timeSeriesData: TimeSeriesDataPoint[];
  subjectChartData: ChartDataPoint[];
  taskTypeChartData: ChartDataPoint[];
  performanceMetrics: any;
  quickStats: any;
  todayProgress: any;
  weekProgress: any;
  formatTime: (seconds: number) => string;
  getStreakMessage: () => string;
  loading: boolean;
  error: string | null;
}

export function OverviewTab({
  analytics,
  quickStats,
  todayProgress,
  weekProgress,
  formatTime,
  getStreakMessage,
  loading,
  error,
}: OverviewTabProps) {
  const { theme } = useTheme();
  
  const renderQuickStats = () => {
    if (!quickStats) return null;

    return (
      <View style={styles(theme).statsGrid}>
        <View style={styles(theme).statCard}>
          <MaterialIcons name="today" size={24} color={theme.colors.accent.primary} />
          <Text style={styles(theme).statValue}>{formatTime(quickStats.todayTime)}</Text>
          <Text style={styles(theme).statLabel}>Today</Text>
        </View>

        <View style={styles(theme).statCard}>
          <MaterialIcons name="view-week" size={24} color={theme.colors.accent.secondary} />
          <Text style={styles(theme).statValue}>{formatTime(quickStats.weekTime)}</Text>
          <Text style={styles(theme).statLabel}>This Week</Text>
        </View>

        <View style={styles(theme).statCard}>
          <MaterialIcons name="calendar-month" size={24} color={theme.colors.status.info} />
          <Text style={styles(theme).statValue}>{formatTime(quickStats.monthTime)}</Text>
          <Text style={styles(theme).statLabel}>This Month</Text>
        </View>

        <View style={styles(theme).statCard}>
          <MaterialIcons name="local-fire-department" size={24} color={theme.colors.status.warning} />
          <Text style={styles(theme).statValue}>{quickStats.currentStreak}</Text>
          <Text style={styles(theme).statLabel}>Day Streak</Text>
        </View>
      </View>
    );
  };

  const renderProgressCards = () => {
    return (
      <View style={styles(theme).progressSection}>
        {/* Today's Progress */}
        {todayProgress && (
          <View style={styles(theme).progressCard}>
            <View style={styles(theme).progressHeader}>
              <Text style={styles(theme).progressTitle}>Today's Goal</Text>
              <Text style={styles(theme).progressPercentage}>
                {Math.round(todayProgress.percentage)}%
              </Text>
            </View>
            <View style={styles(theme).progressBarContainer}>
              <View style={styles(theme).progressBarBackground}>
                <LinearGradient
                  colors={theme.colors.gradients.primary}
                  style={[
                    styles(theme).progressBarFill,
                    { width: `${Math.min(todayProgress.percentage, 100)}%` }
                  ]}
                />
              </View>
            </View>
            <Text style={styles(theme).progressText}>
              {todayProgress.current} / {todayProgress.target} minutes
            </Text>
          </View>
        )}

        {/* Week's Progress */}
        {weekProgress && (
          <View style={styles(theme).progressCard}>
            <View style={styles(theme).progressHeader}>
              <Text style={styles(theme).progressTitle}>Weekly Goal</Text>
              <Text style={styles(theme).progressPercentage}>
                {Math.round(weekProgress.percentage)}%
              </Text>
            </View>
            <View style={styles(theme).progressBarContainer}>
              <View style={styles(theme).progressBarBackground}>
                <LinearGradient
                  colors={theme.colors.gradients.accent}
                  style={[
                    styles(theme).progressBarFill,
                    { width: `${Math.min(weekProgress.percentage, 100)}%` }
                  ]}
                />
              </View>
            </View>
            <Text style={styles(theme).progressText}>
              {weekProgress.current} / {weekProgress.target} minutes
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderStreakCard = () => {
    const streakMessage = getStreakMessage();
    if (!streakMessage) return null;

    return (
      <LinearGradient
        colors={[theme.colors.status.warning, '#D97706']}
        style={styles(theme).streakCard}
      >
        <MaterialIcons name="local-fire-department" size={32} color={theme.colors.text.inverse} />
        <View style={styles(theme).streakContent}>
          <Text style={styles(theme).streakTitle}>Study Streak</Text>
          <Text style={styles(theme).streakMessage}>{streakMessage}</Text>
        </View>
      </LinearGradient>
    );
  };

  const renderInsights = () => {
    if (!analytics) return null;

    const insights = [];

    // Total sessions insight
    if (quickStats?.totalSessions > 0) {
      insights.push({
        icon: 'analytics',
        title: 'Study Sessions',
        text: `You've completed ${quickStats.totalSessions} study sessions. Keep up the consistency!`,
        color: '#6366F1'
      });
    }

    // Top subject insight
    if (analytics.subjectStats.length > 0) {
      const topSubject = analytics.subjectStats[0];
      insights.push({
        icon: 'school',
        title: 'Top Subject',
        text: `${topSubject.subject} is your most studied subject with ${formatTime(topSubject.totalDuration)}.`,
        color: topSubject.color
      });
    }

    // Streak insight
    if (analytics.streakInfo.currentStreak > 0) {
      insights.push({
        icon: 'local-fire-department',
        title: 'Consistency',
        text: `You're on a ${analytics.streakInfo.currentStreak}-day study streak. Amazing dedication!`,
        color: '#F59E0B'
      });
    }

    return (
      <View style={styles(theme).insightsSection}>
        <Text style={styles(theme).sectionTitle}>Insights</Text>
        {insights.map((insight, index) => (
          <View key={index} style={styles(theme).insightCard}>
            <View style={[styles(theme).insightIcon, { backgroundColor: `${insight.color}20` }]}>
              <MaterialIcons name={insight.icon as any} size={20} color={insight.color} />
            </View>
            <View style={styles(theme).insightContent}>
              <Text style={styles(theme).insightTitle}>{insight.title}</Text>
              <Text style={styles(theme).insightText}>{insight.text}</Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles(theme).loadingContainer}>
        <MaterialIcons name="analytics" size={48} color={theme.colors.accent.primary} />
        <Text style={styles(theme).loadingText}>Loading analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles(theme).errorContainer}>
        <MaterialIcons name="error-outline" size={48} color={theme.colors.status.error} />
        <Text style={styles(theme).errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <View style={styles(theme).container}>
      {renderQuickStats()}
      {renderProgressCards()}
      {renderStreakCard()}
      {renderInsights()}
    </View>
  );
}

const styles = (theme: any) => StyleSheet.create({
  container: {
    padding: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    ...theme.shadows.md,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  progressSection: {
    gap: 16,
    marginBottom: 20,
  },
  progressCard: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 20,
    ...theme.shadows.md,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.accent.primary,
  },
  progressBarContainer: {
    marginBottom: 8,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: theme.colors.ui.progressBarTrack,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: theme.colors.text.secondary,
  },
  streakCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
  },
  streakContent: {
    marginLeft: 16,
    flex: 1,
  },
  streakTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text.inverse,
    marginBottom: 4,
  },
  streakMessage: {
    fontSize: 14,
    color: theme.colors.text.inverse,
    opacity: 0.9,
  },
  insightsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  insightCard: {
    flexDirection: 'row',
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    ...theme.shadows.sm,
  },
  insightIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 4,
  },
  insightText: {
    fontSize: 13,
    color: theme.colors.text.secondary,
    lineHeight: 18,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.status.error,
    marginTop: 12,
    textAlign: 'center',
  },
});
