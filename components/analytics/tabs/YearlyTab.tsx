import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, YearlyStat } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';

const { width } = Dimensions.get('window');

interface YearlyTabProps {
  analytics: Analytics | null;
  formatTime: (seconds: number) => string;
  loading: boolean;
  error: string | null;
}

export function YearlyTab({ analytics, formatTime, loading, error }: YearlyTabProps) {
  const { theme } = useTheme();

  const renderYearlyChart = () => {
    if (!analytics?.yearlyStats || analytics.yearlyStats.length === 0) return null;

    const maxDuration = Math.max(...analytics.yearlyStats.map(stat => stat.totalDuration));
    const chartHeight = 120;

    return (
      <View style={styles(theme).chartContainer}>
        <Text style={styles(theme).chartTitle}>Yearly Study Time</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles(theme).barChart}>
            {analytics.yearlyStats.map((stat, index) => {
              const height = maxDuration > 0 ? (stat.totalDuration / maxDuration) * chartHeight : 0;
              const currentYear = new Date().getFullYear();
              const isCurrentYear = stat.year === currentYear;
              
              return (
                <View key={stat.year} style={styles(theme).barContainer}>
                  <View style={[styles(theme).barWrapper, { height: chartHeight }]}>
                    <LinearGradient
                      colors={stat.targetAchieved ? theme.colors.gradients.accent : theme.colors.gradients.primary}
                      style={[
                        styles(theme).bar,
                        { 
                          height: Math.max(height, 4),
                          opacity: isCurrentYear ? 1 : 0.8
                        }
                      ]}
                    />
                  </View>
                  <Text style={[styles(theme).barLabel, isCurrentYear && styles(theme).barLabelCurrent]}>
                    {stat.year}
                  </Text>
                  <Text style={styles(theme).barValue}>
                    {formatTime(stat.totalDuration)}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={12} color={theme.colors.status.success} />
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderYearlyList = () => {
    if (!analytics?.yearlyStats || analytics.yearlyStats.length === 0) return null;

    return (
      <View style={styles(theme).listContainer}>
        <Text style={styles(theme).listTitle}>Yearly Breakdown</Text>
        {analytics.yearlyStats.map((stat, index) => {
          const currentYear = new Date().getFullYear();
          const isCurrentYear = stat.year === currentYear;
          
          const yearLabel = isCurrentYear ? 'This Year' : stat.year.toString();
          const averageMonthly = stat.totalDuration / 12;
          const consistencyPercentage = (stat.activeMonths / 12) * 100;

          return (
            <View key={stat.year} style={styles(theme).yearlyItem}>
              <View style={styles(theme).yearlyHeader}>
                <View style={styles(theme).yearlyDateContainer}>
                  <Text style={[styles(theme).yearlyDate, isCurrentYear && styles(theme).yearlyDateCurrent]}>
                    {yearLabel}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={16} color={theme.colors.status.success} />
                  )}
                </View>
                <Text style={styles(theme).yearlyDuration}>
                  {formatTime(stat.totalDuration)}
                </Text>
              </View>
              
              <View style={styles(theme).yearlyDetails}>
                <View style={styles(theme).yearlyDetailItem}>
                  <MaterialIcons name="play-circle-outline" size={16} color={theme.colors.text.secondary} />
                  <Text style={styles(theme).yearlyDetailText}>
                    {stat.sessionCount} session{stat.sessionCount !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                <View style={styles(theme).yearlyDetailItem}>
                  <MaterialIcons name="calendar-month" size={16} color={theme.colors.text.secondary} />
                  <Text style={styles(theme).yearlyDetailText}>
                    {stat.activeMonths} active month{stat.activeMonths !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                <View style={styles(theme).yearlyDetailItem}>
                  <MaterialIcons name="trending-up" size={16} color={theme.colors.text.secondary} />
                  <Text style={styles(theme).yearlyDetailText}>
                    {formatTime(averageMonthly)} avg/month
                  </Text>
                </View>
                
                <View style={styles(theme).yearlyDetailItem}>
                  <MaterialIcons name="percent" size={16} color={theme.colors.text.secondary} />
                  <Text style={styles(theme).yearlyDetailText}>
                    {consistencyPercentage.toFixed(0)}% consistency
                  </Text>
                </View>
              </View>

              {/* Progress bar for the year */}
              <View style={styles(theme).progressContainer}>
                <View style={styles(theme).progressBar}>
                  <LinearGradient
                    colors={theme.colors.gradients.primary}
                    style={[
                      styles(theme).progressFill,
                      { width: `${Math.min((stat.totalDuration / (365 * 2 * 60 * 60)) * 100, 100)}%` }
                    ]}
                  />
                </View>
                <Text style={styles(theme).progressText}>
                  {Math.round((stat.totalDuration / (365 * 2 * 60 * 60)) * 100)}% of 2h/day goal
                </Text>
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  const renderYearlySummary = () => {
    if (!analytics?.yearlyStats || analytics.yearlyStats.length === 0) return null;

    const totalTime = analytics.yearlyStats.reduce((sum, stat) => sum + stat.totalDuration, 0);
    const totalSessions = analytics.yearlyStats.reduce((sum, stat) => sum + stat.sessionCount, 0);
    const totalActiveMonths = analytics.yearlyStats.reduce((sum, stat) => sum + stat.activeMonths, 0);
    const yearsWithTarget = analytics.yearlyStats.filter(stat => stat.targetAchieved).length;
    const averageYearlyTime = analytics.yearlyStats.length > 0 ? totalTime / analytics.yearlyStats.length : 0;

    return (
      <View style={styles(theme).summaryContainer}>
        <Text style={styles(theme).summaryTitle}>Yearly Summary</Text>
        <View style={styles(theme).summaryGrid}>
          <View style={styles(theme).summaryCard}>
            <Text style={styles(theme).summaryValue}>{formatTime(totalTime)}</Text>
            <Text style={styles(theme).summaryLabel}>Total Time</Text>
          </View>
          <View style={styles(theme).summaryCard}>
            <Text style={styles(theme).summaryValue}>{formatTime(averageYearlyTime)}</Text>
            <Text style={styles(theme).summaryLabel}>Avg/Year</Text>
          </View>
          <View style={styles(theme).summaryCard}>
            <Text style={styles(theme).summaryValue}>{totalSessions}</Text>
            <Text style={styles(theme).summaryLabel}>Total Sessions</Text>
          </View>
          <View style={styles(theme).summaryCard}>
            <Text style={styles(theme).summaryValue}>{yearsWithTarget}</Text>
            <Text style={styles(theme).summaryLabel}>Goals Met</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles(theme).loadingContainer}>
        <MaterialIcons name="calendar-today" size={48} color={theme.colors.accent.primary} />
        <Text style={styles(theme).loadingText}>Loading yearly analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles(theme).errorContainer}>
        <MaterialIcons name="error-outline" size={48} color={theme.colors.status.error} />
        <Text style={styles(theme).errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles(theme).container} showsVerticalScrollIndicator={false}>
      {renderYearlySummary()}
      {renderYearlyChart()}
      {renderYearlyList()}
    </ScrollView>
  );
}

const styles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryContainer: {
    margin: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: theme.colors.text.secondary,
  },
  chartContainer: {
    backgroundColor: theme.colors.background.card,
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    ...theme.shadows.md,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
    paddingBottom: 40,
  },
  barContainer: {
    alignItems: 'center',
    minWidth: 60,
  },
  barWrapper: {
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 40,
    borderRadius: 20,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 10,
    color: theme.colors.text.secondary,
    marginBottom: 2,
    textAlign: 'center',
  },
  barLabelCurrent: {
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  barValue: {
    fontSize: 9,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  listContainer: {
    margin: 20,
    marginTop: 0,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  yearlyItem: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    ...theme.shadows.sm,
  },
  yearlyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  yearlyDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  yearlyDate: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  yearlyDateCurrent: {
    color: theme.colors.accent.primary,
    fontWeight: '600',
  },
  yearlyDuration: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.accent.primary,
  },
  yearlyDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 12,
  },
  yearlyDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  yearlyDetailText: {
    fontSize: 12,
    color: theme.colors.text.secondary,
  },
  progressContainer: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.ui.border,
    paddingTop: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: theme.colors.ui.progressBarTrack,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.status.error,
    marginTop: 12,
    textAlign: 'center',
  },
});
