import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { SessionSummary, TaskType, TASK_TYPES } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';
import { Button, Input, Card } from '@/components/ui';

interface SessionFeedbackCardProps {
  onSubmit: (summary: SessionSummary) => void;
  onSkip: () => void;
  initialTaskName?: string;
  initialTaskType?: TaskType;
  isLoading?: boolean;
  showTaskDetails?: boolean;
  showProductivityRating?: boolean;
  showNotes?: boolean;
  showFeedback?: boolean;
  title?: string;
  subtitle?: string;
}

export function SessionFeedbackCard({
  onSubmit,
  onSkip,
  initialTaskName = '',
  initialTaskType = 'General Study',
  isLoading = false,
  showTaskDetails = true,
  showProductivityRating = true,
  showNotes = true,
  showFeedback = true,
  title = 'Session Feedback',
  subtitle = 'Help us understand how your session went',
}: SessionFeedbackCardProps) {
  const { theme } = useTheme();
  
  const [taskName, setTaskName] = useState(initialTaskName);
  const [taskType, setTaskType] = useState<TaskType>(initialTaskType);
  const [productivityRating, setProductivityRating] = useState<number>(0);
  const [notes, setNotes] = useState('');
  const [feedback, setFeedback] = useState('');

  const fadeAnim = new Animated.Value(1);

  const handleSubmit = () => {
    if (isLoading) return;

    const summary: SessionSummary = {
      taskName: showTaskDetails && taskName.trim() ? taskName.trim() : undefined,
      taskType: showTaskDetails ? taskType : undefined,
      productivityRating: showProductivityRating && productivityRating > 0 ? productivityRating : undefined,
      notes: showNotes && notes.trim() ? notes.trim() : undefined,
      feedback: showFeedback && feedback.trim() ? feedback.trim() : undefined,
    };

    onSubmit(summary);
  };

  const renderProductivityRating = () => {
    if (!showProductivityRating) return null;

    const ratingLabels = [
      'Not productive',
      'Somewhat productive', 
      'Moderately productive',
      'Very productive',
      'Extremely productive'
    ];

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
          Productivity Rating
        </Text>
        <Text style={[styles.sectionSubtitle, { color: theme.colors.text.secondary }]}>
          How productive did you feel during this session?
        </Text>
        
        <View style={[styles.ratingContainer, { backgroundColor: theme.colors.background.secondary }]}>
          <View style={styles.starContainer}>
            {[1, 2, 3, 4, 5].map((star) => (
              <Animated.View
                key={star}
                style={{
                  transform: [{
                    scale: productivityRating === star ? 1.1 : 1
                  }]
                }}
              >
                <Button
                  onPress={() => setProductivityRating(star)}
                  variant="ghost"
                  size="sm"
                  style={[
                    styles.starButton,
                    {
                      backgroundColor: star <= productivityRating
                        ? theme.colors.primary + '20'
                        : 'transparent'
                    }
                  ]}
                  title="" // Button requires a title, even if empty
                  icon={
                    <MaterialIcons
                      name={star <= productivityRating ? 'star' : 'star-border'}
                      size={24}
                      color={star <= productivityRating ? theme.colors.primary : theme.colors.text.secondary}
                    />
                  }
                />
              </Animated.View>
            ))}
          </View>
          {productivityRating > 0 && (
            <Text style={[styles.ratingLabel, { color: theme.colors.text.secondary }]}>
              {ratingLabels[productivityRating - 1]}
            </Text>
          )}
        </View>
      </View>
    );
  };

  const renderTaskTypeSelector = () => {
    if (!showTaskDetails) return null;

    return (
      <View style={styles.taskTypeContainer}>
        <Text style={[styles.inputLabel, { color: theme.colors.text.primary }]}>
          Task Type
        </Text>
        <View style={styles.taskTypeGrid}>
          {TASK_TYPES.slice(0, 6).map((type) => (
            <Button
              key={type}
              title={type}
              onPress={() => setTaskType(type)}
              variant={taskType === type ? "primary" : "outline"}
              size="sm"
              style={[
                styles.taskTypeChip,
                {
                  backgroundColor: taskType === type
                    ? theme.colors.primary
                    : theme.colors.background.secondary,
                  borderColor: taskType === type
                    ? theme.colors.primary
                    : theme.colors.ui.border,
                }
              ]}
              textStyle={{
                color: taskType === type
                  ? theme.colors.text.inverse
                  : theme.colors.text.primary,
                fontSize: 12,
                fontWeight: '500' as const,
              }}
            />
          ))}
        </View>
      </View>
    );
  };

  return (
    <Animated.View style={{ opacity: fadeAnim }}>
      <Card
        variant="elevated"
        style={[
          styles.container,
          { 
            backgroundColor: theme.colors.background.card,
            borderColor: theme.colors.ui.border,
          }
        ]}
      >
        {/* Header */}
        <View style={styles.header}>
          <MaterialIcons 
            name="feedback" 
            size={24} 
            color={theme.colors.primary} 
            style={styles.headerIcon}
          />
          <View style={styles.headerText}>
            <Text style={[styles.title, { color: theme.colors.text.primary }]}>
              {title}
            </Text>
            <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
              {subtitle}
            </Text>
          </View>
        </View>

        {/* Task Details */}
        {showTaskDetails && (
          <View style={styles.section}>
            <Input
              label="What did you work on?"
              placeholder="e.g., Chapter 5 exercises, Essay writing..."
              value={taskName}
              onChangeText={setTaskName}
              maxLength={100}
              variant="default"
              style={{ backgroundColor: theme.colors.background.secondary }}
            />
            {renderTaskTypeSelector()}
          </View>
        )}

        {/* Productivity Rating */}
        {renderProductivityRating()}

        {/* Notes */}
        {showNotes && (
          <View style={styles.section}>
            <Input
              label="Session Notes (Optional)"
              placeholder="Any notes about this session..."
              value={notes}
              onChangeText={setNotes}
              multiline
              maxLength={200}
              variant="default"
              style={{...styles.notesInput, backgroundColor: theme.colors.background.secondary}}
            />
          </View>
        )}

        {/* Feedback */}
        {showFeedback && (
          <View style={styles.section}>
            <Input
              label="Additional Feedback (Optional)"
              placeholder="How did this session go? Any challenges or insights?"
              value={feedback}
              onChangeText={setFeedback}
              multiline
              maxLength={200}
              variant="default"
              style={{...styles.notesInput, backgroundColor: theme.colors.background.secondary}}
            />
          </View>
        )}

        {/* Actions */}
        <View style={styles.actions}>
          <Button
            title="Skip"
            onPress={onSkip}
            variant="outline"
            size="md"
            style={[
              styles.actionButton,
              { 
                borderColor: theme.colors.ui.border,
                backgroundColor: theme.colors.background.primary,
              }
            ]}
            textStyle={{ color: theme.colors.text.secondary }}
            disabled={isLoading}
          />
          <Button
            title={isLoading ? "Saving..." : "Save Feedback"}
            onPress={handleSubmit}
            variant="primary"
            size="md"
            style={[
              styles.actionButton,
              styles.primaryButton,
              { backgroundColor: theme.colors.primary }
            ]}
            textStyle={{ color: theme.colors.text.inverse }}
            loading={isLoading}
            disabled={isLoading}
            icon={
              !isLoading ? (
                <MaterialIcons
                  name="check"
                  size={18}
                  color={theme.colors.text.inverse}
                />
              ) : undefined
            }
            iconPosition="right"
          />
        </View>
      </Card>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    margin: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerIcon: {
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  taskTypeContainer: {
    marginTop: 16,
  },
  taskTypeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  taskTypeChip: {
    borderRadius: 20,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  ratingContainer: {
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
  },
  starContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 12,
    gap: 4,
  },
  starButton: {
    borderRadius: 16,
    padding: 6,
  },
  ratingLabel: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '500',
    fontStyle: 'italic',
  },
  notesInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  actionButton: {
    flex: 1,
    borderRadius: 12,
  },
  primaryButton: {
    flex: 2,
  },
});
