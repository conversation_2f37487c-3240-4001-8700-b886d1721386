import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Dimensions,
  Alert,
  Animated,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { SessionSummary, TaskType, TASK_TYPES, TimerMode } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';
import { Button, Input, Modal } from '@/components/ui';
import { useSessionStore } from '@/stores/sessionStore';

interface SessionSummaryModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (summary: SessionSummary) => void;
  sessionId?: string;
  sessionDuration: number;
  subject?: string;
  subjectColor?: string;
  mode?: TimerMode;
  phase?: 'work' | 'shortBreak' | 'longBreak';
  initialTaskName?: string;
  initialTaskType?: TaskType;
  userId: string;
}

const { width } = Dimensions.get('window');

export function SessionSummaryModal({
  visible,
  onClose,
  onSubmit,
  sessionId,
  sessionDuration,
  subject,
  subjectColor,
  mode = 'stopwatch',
  phase,
  initialTaskName = '',
  initialTaskType = 'General Study',
  userId,
}: SessionSummaryModalProps) {
  const { theme } = useTheme();
  const { completeSession, isLoading } = useSessionStore();

  const [taskName, setTaskName] = useState(initialTaskName);
  const [taskType, setTaskType] = useState<TaskType>(initialTaskType);
  const [productivityRating, setProductivityRating] = useState<number>(0);
  const [notes, setNotes] = useState('');
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(0);
      slideAnim.setValue(50);
    }
  }, [visible]);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const getSessionTypeLabel = (): string => {
    if (mode === 'pomodoro') {
      switch (phase) {
        case 'work':
          return 'Pomodoro Work Session';
        case 'shortBreak':
          return 'Short Break';
        case 'longBreak':
          return 'Long Break';
        default:
          return 'Pomodoro Session';
      }
    }
    return 'Study Session';
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;

    setIsSubmitting(true);

    try {
      const summary: SessionSummary = {
        taskName: taskName.trim() || '',
        taskType,
        productivityRating: productivityRating > 0 ? productivityRating : undefined,
        notes: notes.trim() || undefined,
        feedback: feedback.trim() || undefined,
      };

      // If sessionId is provided, complete the session in the store
      if (sessionId) {
        await completeSession(sessionId, summary);
      }

      onSubmit(summary);
      resetForm();
      onClose();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save session');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkip = () => {
    onSubmit({});
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setTaskName('');
    setTaskType('General Study');
    setProductivityRating(0);
    setNotes('');
    setFeedback('');
  };

  const renderStarRating = () => {
    const ratingLabels = [
      'Not productive',
      'Somewhat productive',
      'Moderately productive',
      'Very productive',
      'Extremely productive'
    ];

    return (
      <View style={[styles.ratingContainer, { backgroundColor: theme.colors.background.card }]}>
        <View style={styles.starContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <Animated.View
              key={star}
              style={{
                transform: [{
                  scale: productivityRating === star ? 1.2 : 1
                }]
              }}
            >
              <Button
                title=""
                onPress={() => setProductivityRating(star)}
                variant="ghost"
                size="sm"
                style={[
                  styles.starButton,
                  {
                    backgroundColor: star <= productivityRating
                      ? theme.colors.accent.primary + '20'
                      : 'transparent'
                  }
                ] as any}
                icon={
                  <MaterialIcons
                    name={star <= productivityRating ? 'star' : 'star-border'}
                    size={28}
                    color={star <= productivityRating ? theme.colors.accent.primary : theme.colors.text.secondary}
                  />
                }
              />
            </Animated.View>
          ))}
        </View>
        {productivityRating > 0 && (
          <Animated.View style={{ opacity: fadeAnim }}>
            <Text style={[styles.ratingLabel, { color: theme.colors.text.secondary }]}>
              {ratingLabels[productivityRating - 1]}
            </Text>
          </Animated.View>
        )}
      </View>
    );
  };

  const renderTaskTypeSelector = () => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.taskTypeScroll}
        contentContainerStyle={styles.taskTypeContent}
      >
        {TASK_TYPES.map((type) => (
          <Button
            key={type}
            title={type}
            onPress={() => setTaskType(type)}
            variant={taskType === type ? "primary" : "outline"}
            size="sm"
            style={[
              styles.taskTypeChip,
              {
                backgroundColor: taskType === type
                  ? theme.colors.accent.primary
                  : theme.colors.background.card,
                borderColor: taskType === type
                  ? theme.colors.accent.primary
                  : theme.colors.ui.border,
              }
            ] as any}
            textStyle={{
              color: taskType === type
                ? theme.colors.text.inverse
                : theme.colors.text.primary,
              fontSize: 14,
              fontWeight: '500',
            } as any}
          />
        ))}
      </ScrollView>
    );
  };

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      size="lg"
      style={{ backgroundColor: theme.colors.background.primary }}
    >
      <Animated.View
        style={[
          styles.container,
          {
            backgroundColor: theme.colors.background.primary,
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.colors.background.card }]}>
          <Button
            title=""
            onPress={onClose}
            variant="ghost"
            size="sm"
            style={styles.closeButton}
            icon={<MaterialIcons name="close" size={24} color={theme.colors.text.secondary} />}
          />
          <Text style={[styles.title, { color: theme.colors.text.primary }]}>
            {getSessionTypeLabel()} Complete! 🎉
          </Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Session Summary Card */}
          <Animated.View
            style={[
              styles.summaryCard,
              {
                backgroundColor: theme.colors.background.card,
                borderColor: theme.colors.ui.border,
                opacity: fadeAnim,
              }
            ]}
          >
            <View style={styles.summaryHeader}>
              <MaterialIcons
                name="timer"
                size={24}
                color={theme.colors.accent.primary}
                style={styles.summaryIcon}
              />
              <Text style={[styles.summaryTitle, { color: theme.colors.text.primary }]}>
                Session Summary
              </Text>
            </View>

            <View style={styles.summaryContent}>
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
                  Duration:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
                  {formatDuration(sessionDuration)}
                </Text>
              </View>

              {subject && (
                <View style={styles.summaryRow}>
                  <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
                    Subject:
                  </Text>
                  <View style={styles.subjectContainer}>
                    {subjectColor && (
                      <View
                        style={[
                          styles.subjectColorDot,
                          { backgroundColor: subjectColor }
                        ]}
                      />
                    )}
                    <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
                      {subject}
                    </Text>
                  </View>
                </View>
              )}

              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
                  Type:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
                  {getSessionTypeLabel()}
                </Text>
              </View>
            </View>
          </Animated.View>

          {/* Task Details */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              What did you work on?
            </Text>
            <Input
              placeholder="e.g., Chapter 5 exercises, Essay writing..."
              value={taskName}
              onChangeText={setTaskName}
              multiline
              maxLength={100}
              variant="default"
              style={[styles.textInput, { backgroundColor: theme.colors.background.card }] as any}
            />
          </Animated.View>

          {/* Task Type */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Task Type
            </Text>
            {renderTaskTypeSelector()}
          </Animated.View>

          {/* Productivity Rating */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              How productive did you feel?
            </Text>
            <Text style={[styles.sectionSubtitle, { color: theme.colors.text.secondary }]}>
              Rate your focus and productivity level
            </Text>
            {renderStarRating()}
          </Animated.View>

          {/* Notes */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Session Notes
              <Text style={[styles.optionalText, { color: theme.colors.text.secondary }]}>
                {' '}(Optional)
              </Text>
            </Text>
            <Input
              placeholder="Any notes about this session..."
              value={notes}
              onChangeText={setNotes}
              multiline
              maxLength={200}
              variant="default"
              style={[styles.notesInput, { backgroundColor: theme.colors.background.card }] as any}
            />
          </Animated.View>

          {/* Feedback */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Session Feedback
              <Text style={[styles.optionalText, { color: theme.colors.text.secondary }]}>
                {' '}(Optional)
              </Text>
            </Text>
            <Input
              placeholder="How did this session go? Any challenges or insights?"
              value={feedback}
              onChangeText={setFeedback}
              multiline
              maxLength={200}
              variant="default"
              style={[styles.notesInput, { backgroundColor: theme.colors.background.card }] as any}
            />
          </Animated.View>
        </ScrollView>

        {/* Action Buttons */}
        <Animated.View
          style={[
            styles.actions,
            {
              backgroundColor: theme.colors.background.card,
              borderTopColor: theme.colors.ui.border,
              opacity: fadeAnim,
            }
          ]}
        >
          <Button
            title="Skip"
            onPress={handleSkip}
            variant="outline"
            size="lg"
            style={[
              styles.actionButton,
              {
                borderColor: theme.colors.ui.border,
                backgroundColor: theme.colors.background.primary,
              }
            ]}
            textStyle={{ color: theme.colors.text.secondary } as any}
            disabled={isSubmitting}
          />
          <Button
            title={isSubmitting ? "Saving..." : "Save Session"}
            onPress={handleSubmit}
            variant="primary"
            size="lg"
            style={[
              styles.actionButton,
              styles.primaryButton,
              { backgroundColor: theme.colors.accent.primary }
            ]}
            textStyle={{ color: theme.colors.text.inverse } as any}
            loading={isSubmitting}
            disabled={isSubmitting}
            icon={
              !isSubmitting ? (
                <MaterialIcons
                  name="check"
                  size={20}
                  color={theme.colors.text.inverse}
                />
              ) : undefined
            }
            iconPosition="right"
          />
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  summaryCard: {
    borderRadius: 16,
    padding: 20,
    marginTop: 20,
    marginBottom: 24,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryIcon: {
    marginRight: 8,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  summaryContent: {
    gap: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  subjectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subjectColorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  section: {
    marginBottom: 28,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  optionalText: {
    fontSize: 14,
    fontWeight: '400',
  },
  textInput: {
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    minHeight: 48,
  },
  notesInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  taskTypeScroll: {
    marginBottom: 8,
  },
  taskTypeContent: {
    paddingRight: 20,
  },
  taskTypeChip: {
    borderRadius: 24,
    marginRight: 12,
    borderWidth: 1,
    minWidth: 80,
  },
  ratingContainer: {
    borderRadius: 16,
    padding: 20,
    marginVertical: 8,
  },
  starContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
    gap: 8,
  },
  starButton: {
    borderRadius: 20,
    padding: 8,
  },
  ratingLabel: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '500',
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    gap: 16,
  },
  actionButton: {
    flex: 1,
    borderRadius: 12,
  },
  primaryButton: {
    flex: 2,
  },
});
