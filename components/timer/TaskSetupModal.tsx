import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, Alert } from 'react-native';
import { Modal, Button } from '@/components/ui';
import { useTheme } from '@/contexts/ThemeContext';
import { TaskType } from '@/types/app';
import { Picker } from '@react-native-picker/picker';

interface TaskSetupModalProps {
  visible: boolean;
  onClose: () => void;
  onStartTimer: (taskName: string, taskType: TaskType) => void;
  initialTaskName?: string;
  initialTaskType?: TaskType;
}

export const TaskSetupModal: React.FC<TaskSetupModalProps> = ({
  visible,
  onClose,
  onStartTimer,
  initialTaskName = '',
  initialTaskType = 'General Study',
}) => {
  const { theme } = useTheme();
  const [taskName, setTaskName] = useState(initialTaskName);
  const [taskType, setTaskType] = useState<TaskType>(initialTaskType);

  const taskTypes: TaskType[] = [
    'Lecture',
    'Exercise',
    'Reading',
    'Practice',
    'Review',
    'General Study',
    'Custom',
  ];

  const handleStart = () => {
    if (!taskName.trim()) {
      Alert.alert('Task Name Required', 'Please enter a task name or description.');
      return;
    }
    onStartTimer(taskName, taskType);
    onClose();
  };

  return (
    <Modal visible={visible} onClose={onClose} title="Start New Session" size="md" scrollable={false}>
      <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
        <Text style={[styles.label, { color: theme.colors.text.primary }]}>Task Name / Description</Text>
        <TextInput
          style={[
            styles.input,
            {
              backgroundColor: theme.colors.background.secondary,
              color: theme.colors.text.primary,
              borderColor: theme.colors.ui.border,
            },
          ]}
          placeholder="e.g., Study for Exam"
          placeholderTextColor={theme.colors.text.secondary}
          value={taskName}
          onChangeText={setTaskName}
          autoCapitalize="sentences"
          returnKeyType="done"
        />

        <Text style={[styles.label, { color: theme.colors.text.primary }]}>Task Type</Text>
        <View
          style={[
            styles.pickerContainer,
            {
              backgroundColor: theme.colors.background.secondary,
              borderColor: theme.colors.ui.border,
            },
          ]}
        >
          <Picker
            selectedValue={taskType}
            onValueChange={(itemValue: TaskType) => setTaskType(itemValue)}
            style={[styles.picker, { color: theme.colors.text.primary }]}
            itemStyle={{ color: theme.colors.text.primary }}
          >
            {taskTypes.map((type) => (
              <Picker.Item key={type} label={type} value={type} />
            ))}
          </Picker>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="Cancel"
            onPress={onClose}
            variant="outline"
            style={styles.button}
            textStyle={{ color: theme.colors.text.secondary }}
          />
          <Button
            title="Start Session"
            onPress={handleStart}
            variant="primary"
            style={[styles.button, { backgroundColor: theme.colors.accent.primary }]}
            textStyle={{ color: theme.colors.text.inverse }}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  pickerContainer: {
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 20,
    overflow: 'hidden',
    height: 150, // Increased height for better visibility
  },
  picker: {
    height: 150, // Increased height to match container
    width: '100%',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  button: {
    flex: 1,
    borderRadius: 12,
  },
});
