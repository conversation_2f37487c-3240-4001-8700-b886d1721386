import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, Alert, ScrollView, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { <PERSON><PERSON>, Button } from '@/components/ui';
import { useTheme } from '@/contexts/ThemeContext';
import { TaskType } from '@/types/app';

interface TaskSetupModalProps {
  visible: boolean;
  onClose: () => void;
  onStartTimer: (taskName: string, taskType: TaskType) => void;
  initialTaskName?: string;
  initialTaskType?: TaskType;
}

export const TaskSetupModal: React.FC<TaskSetupModalProps> = ({
  visible,
  onClose,
  onStartTimer,
  initialTaskName = '',
  initialTaskType = 'General Study',
}) => {
  const { theme } = useTheme();
  const [taskName, setTaskName] = useState(initialTaskName);
  const [taskType, setTaskType] = useState<TaskType>(initialTaskType);

  const taskTypes: TaskType[] = [
    'Lecture',
    'Exercise',
    'Reading',
    'Practice',
    'Review',
    'General Study',
    'Custom',
  ];

  const handleStart = () => {
    if (!taskName.trim()) {
      Alert.alert('Task Name Required', 'Please enter a task name or description.');
      return;
    }
    onStartTimer(taskName, taskType);
    onClose();
  };

  const getTaskTypeIcon = (type: TaskType): string => {
    switch (type) {
      case 'Lecture': return 'school';
      case 'Exercise': return 'fitness-center';
      case 'Reading': return 'menu-book';
      case 'Practice': return 'psychology';
      case 'Review': return 'rate-review';
      case 'General Study': return 'library-books';
      case 'Custom': return 'edit';
      default: return 'library-books';
    }
  };

  return (
    <Modal visible={visible} onClose={onClose} size="md" scrollable={false} showCloseButton={false}>
      <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
        {/* Header */}
        <LinearGradient
          colors={[theme.colors.accent.primary + '15', theme.colors.background.primary]}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <View style={styles.titleContainer}>
                <LinearGradient
                  colors={[theme.colors.accent.primary, theme.colors.accent.secondary || theme.colors.accent.primary]}
                  style={styles.startIcon}
                >
                  <MaterialIcons name="play-arrow" size={28} color="#FFFFFF" />
                </LinearGradient>
                <Text style={[styles.title, { color: theme.colors.text.primary }]}>
                  Start New Session
                </Text>
              </View>
              <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
                Set up your focus session with a clear goal
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.closeButton, { backgroundColor: theme.colors.background.secondary }]}
              onPress={onClose}
            >
              <MaterialIcons name="close" size={24} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Task Name Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              What will you work on?
            </Text>
            <Text style={[styles.sectionSubtitle, { color: theme.colors.text.secondary }]}>
              Describe your task or study goal
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: theme.colors.background.card,
                  color: theme.colors.text.primary,
                  borderColor: theme.colors.ui.border,
                },
              ]}
              placeholder="e.g., Study for Math Exam, Read Chapter 5..."
              placeholderTextColor={theme.colors.text.secondary}
              value={taskName}
              onChangeText={setTaskName}
              autoCapitalize="sentences"
              returnKeyType="done"
              multiline
              maxLength={100}
            />
          </View>

          {/* Task Type Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Task Type
            </Text>
            <Text style={[styles.sectionSubtitle, { color: theme.colors.text.secondary }]}>
              Choose the type of work you'll be doing
            </Text>
            <View style={styles.taskTypeGrid}>
              {taskTypes.map((type) => (
                <TouchableOpacity
                  key={type}
                  style={[
                    styles.taskTypeChip,
                    {
                      backgroundColor: taskType === type
                        ? theme.colors.accent.primary + '20'
                        : theme.colors.background.card,
                      borderColor: taskType === type
                        ? theme.colors.accent.primary
                        : theme.colors.ui.border,
                    }
                  ]}
                  onPress={() => setTaskType(type)}
                >
                  <MaterialIcons
                    name={getTaskTypeIcon(type) as any}
                    size={20}
                    color={taskType === type ? theme.colors.accent.primary : theme.colors.text.secondary}
                  />
                  <Text
                    style={[
                      styles.taskTypeText,
                      {
                        color: taskType === type ? theme.colors.accent.primary : theme.colors.text.primary,
                        fontWeight: taskType === type ? '600' : '500',
                      }
                    ]}
                  >
                    {type}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <LinearGradient
          colors={[theme.colors.background.primary + '00', theme.colors.background.primary]}
          style={styles.actions}
        >
          <Button
            title="Cancel"
            onPress={onClose}
            variant="outline"
            size="lg"
            style={[
              styles.actionButton,
              {
                borderColor: theme.colors.ui.border,
                backgroundColor: theme.colors.background.secondary,
              }
            ]}
            textStyle={{ color: theme.colors.text.secondary }}
          />
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.primaryButton,
            ]}
            onPress={handleStart}
          >
            <LinearGradient
              colors={[theme.colors.accent.primary, theme.colors.accent.secondary || theme.colors.accent.primary]}
              style={styles.primaryButtonGradient}
            >
              <MaterialIcons name="play-arrow" size={20} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={[styles.primaryButtonText, { color: theme.colors.text.inverse }]}>
                Start Session
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </LinearGradient>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerGradient: {
    paddingBottom: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 20,
  },
  headerContent: {
    flex: 1,
    paddingRight: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  startIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  closeButton: {
    padding: 12,
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    lineHeight: 30,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    opacity: 0.8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 15,
    marginBottom: 20,
    lineHeight: 22,
    opacity: 0.7,
  },
  input: {
    borderWidth: 1.5,
    borderRadius: 16,
    padding: 18,
    fontSize: 16,
    fontWeight: '500',
    minHeight: 56,
    textAlignVertical: 'top',
  },
  taskTypeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  taskTypeChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 18,
    borderRadius: 28,
    borderWidth: 1.5,
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  taskTypeText: {
    fontSize: 15,
    marginLeft: 8,
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 24,
    gap: 16,
  },
  actionButton: {
    flex: 1,
    borderRadius: 16,
    minHeight: 56,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  primaryButton: {
    flex: 2,
    overflow: 'hidden',
  },
  primaryButtonGradient: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 16,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '700',
    marginLeft: 8,
  },
  buttonIcon: {
    marginRight: 4,
  },
});
