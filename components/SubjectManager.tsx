import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  Alert,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Plus, 
  X, 
  Edit3, 
  Trash2, 
  Check, 
  AlertCircle,
  Palette,
  Search
} from 'lucide-react-native';
import { Subject } from '@/types/app';
import { useSubjectStore } from '@/stores/subjectStore';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  SUBJECT_COLORS, 
  ALL_SUBJECT_COLORS, 
  getRandomSubjectColor,
  isValidHexColor,
  getContrastingTextColor 
} from '@/constants/subjectColors';

interface SubjectManagerProps {
  selectedSubject: Subject | null;
  onSelectSubject: (subject: Subject | null) => void;
  showManageButton?: boolean;
}

const { width } = Dimensions.get('window');

export default function SubjectManager({ 
  selectedSubject, 
  onSelectSubject, 
  showManageButton = true 
}: SubjectManagerProps) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const {
    subjects,
    isLoading,
    error,
    fetchSubjects,
    addSubject,
    updateSubject,
    deleteSubject,
    validateSubjectName,
    subscribeToRealtime,
    unsubscribeFromRealtime,
  } = useSubjectStore();

  // Modal states
  const [showSubjectModal, setShowSubjectModal] = useState(false);
  const [showManageModal, setShowManageModal] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  
  // Form states
  const [subjectName, setSubjectName] = useState('');
  const [selectedColor, setSelectedColor] = useState(getRandomSubjectColor());
  const [customColor, setCustomColor] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);

  // Load subjects on mount
  useEffect(() => {
    if (user) {
      fetchSubjects(user.id);
      subscribeToRealtime(user.id);
    }

    return () => {
      unsubscribeFromRealtime();
    };
  }, [user]);

  // Filter subjects based on search
  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const openAddModal = () => {
    setEditingSubject(null);
    setSubjectName('');
    setSelectedColor(getRandomSubjectColor());
    setCustomColor('');
    setShowSubjectModal(true);
  };

  const openEditModal = (subject: Subject) => {
    setEditingSubject(subject);
    setSubjectName(subject.name);
    setSelectedColor(subject.color);
    setCustomColor(subject.color);
    setShowSubjectModal(true);
  };

  const closeModal = () => {
    setShowSubjectModal(false);
    setEditingSubject(null);
    setSubjectName('');
    setSelectedColor(getRandomSubjectColor());
    setCustomColor('');
    setShowColorPicker(false);
  };

  const handleSaveSubject = async () => {
    if (!user || !subjectName.trim()) {
      Alert.alert('Error', 'Please enter a subject name');
      return;
    }

    const finalColor = customColor && isValidHexColor(customColor) ? customColor : selectedColor;

    try {
      if (editingSubject) {
        // Update existing subject
        if (!validateSubjectName(subjectName, editingSubject.id)) {
          Alert.alert('Error', 'A subject with this name already exists');
          return;
        }

        const updatedSubject = await updateSubject(editingSubject.id, {
          name: subjectName.trim(),
          color: finalColor,
        });

        // Update selected subject if it was the one being edited
        if (selectedSubject?.id === editingSubject.id) {
          onSelectSubject(updatedSubject);
        }

        Alert.alert('Success', 'Subject updated successfully');
      } else {
        // Create new subject
        if (!validateSubjectName(subjectName)) {
          Alert.alert('Error', 'A subject with this name already exists');
          return;
        }

        const newSubject = await addSubject(user.id, subjectName.trim(), finalColor);
        onSelectSubject(newSubject);
        Alert.alert('Success', 'Subject created successfully');
      }

      closeModal();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save subject');
    }
  };

  const handleDeleteSubject = (subject: Subject) => {
    Alert.alert(
      'Delete Subject',
      `Are you sure you want to delete "${subject.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteSubject(subject.id);
              
              // Clear selection if deleted subject was selected
              if (selectedSubject?.id === subject.id) {
                onSelectSubject(null);
              }
              
              Alert.alert('Success', 'Subject deleted successfully');
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to delete subject');
            }
          },
        },
      ]
    );
  };

  const renderColorPalette = () => (
    <View style={styles.colorPalette}>
      <Text style={[styles.colorLabel, { color: theme.colors.text.primary }]}>
        Choose Color
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.colorRow}>
          {SUBJECT_COLORS.map((color) => (
            <TouchableOpacity
              key={color}
              style={[
                styles.colorOption,
                { backgroundColor: color },
                selectedColor === color && styles.selectedColorOption,
              ]}
              onPress={() => {
                setSelectedColor(color);
                setCustomColor(color);
              }}
            />
          ))}
        </View>
      </ScrollView>
      
      <View style={styles.customColorSection}>
        <Text style={[styles.customColorLabel, { color: theme.colors.text.secondary }]}>
          Custom Color (Hex)
        </Text>
        <View style={styles.customColorInput}>
          <TextInput
            style={[
              styles.colorInput,
              { 
                backgroundColor: theme.colors.background.secondary,
                color: theme.colors.text.primary,
                borderColor: theme.colors.ui.border,
              }
            ]}
            value={customColor}
            onChangeText={(text) => {
              setCustomColor(text);
              if (isValidHexColor(text)) {
                setSelectedColor(text);
              }
            }}
            placeholder="#6366f1"
            placeholderTextColor={theme.colors.text.tertiary}
            maxLength={7}
          />
          <View
            style={[
              styles.colorPreview,
              { 
                backgroundColor: isValidHexColor(customColor) ? customColor : selectedColor,
                borderColor: theme.colors.ui.border,
              }
            ]}
          />
        </View>
      </View>
    </View>
  );

  return (
    <>
      <View style={styles.container}>
        <Text style={[styles.label, { color: theme.colors.text.primary }]}>Subject</Text>
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.subjectList}>
          {filteredSubjects.map((subject) => (
            <TouchableOpacity
              key={subject.id}
              style={[
                styles.subjectChip,
                { 
                  backgroundColor: theme.colors.background.secondary,
                  borderColor: theme.colors.ui.border,
                },
                selectedSubject?.id === subject.id && {
                  backgroundColor: theme.colors.background.tertiary,
                  borderColor: subject.color,
                  borderWidth: 2,
                },
              ]}
              onPress={() => onSelectSubject(subject)}
              onLongPress={() => openEditModal(subject)}
            >
              <View style={[styles.colorDot, { backgroundColor: subject.color }]} />
              <Text
                style={[
                  styles.subjectText,
                  { color: theme.colors.text.secondary },
                  selectedSubject?.id === subject.id && { 
                    color: theme.colors.accent.primary,
                    fontWeight: '600',
                  },
                ]}
                numberOfLines={1}
              >
                {subject.name}
              </Text>
            </TouchableOpacity>
          ))}
          
          <TouchableOpacity
            style={[
              styles.addButton,
              { 
                backgroundColor: theme.colors.background.secondary,
                borderColor: theme.colors.ui.border,
              },
            ]}
            onPress={openAddModal}
          >
            <Plus size={16} color={theme.colors.accent.primary} />
          </TouchableOpacity>
        </ScrollView>

        {showManageButton && (
          <TouchableOpacity
            style={[styles.manageButton, { backgroundColor: theme.colors.accent.primary }]}
            onPress={() => setShowManageModal(true)}
          >
            <Text style={[styles.manageButtonText, { color: theme.colors.text.inverse }]}>
              Manage Subjects
            </Text>
          </TouchableOpacity>
        )}

        {error && (
          <View style={styles.errorContainer}>
            <AlertCircle size={16} color={theme.colors.status.error} />
            <Text style={[styles.errorText, { color: theme.colors.status.error }]}>
              {error}
            </Text>
          </View>
        )}
      </View>

      {/* Add/Edit Subject Modal */}
      <Modal
        visible={showSubjectModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={closeModal}
      >
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background.primary }]}>
          <View style={[styles.modalHeader, { borderBottomColor: theme.colors.ui.border }]}>
            <TouchableOpacity onPress={closeModal}>
              <X size={24} color={theme.colors.text.primary} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: theme.colors.text.primary }]}>
              {editingSubject ? 'Edit Subject' : 'Add Subject'}
            </Text>
            <TouchableOpacity onPress={handleSaveSubject} disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator size="small" color={theme.colors.accent.primary} />
              ) : (
                <Check size={24} color={theme.colors.accent.primary} />
              )}
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            <View style={styles.inputSection}>
              <Text style={[styles.inputLabel, { color: theme.colors.text.primary }]}>
                Subject Name
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: theme.colors.background.secondary,
                    color: theme.colors.text.primary,
                    borderColor: theme.colors.ui.border,
                  }
                ]}
                value={subjectName}
                onChangeText={setSubjectName}
                placeholder="Enter subject name"
                placeholderTextColor={theme.colors.text.tertiary}
                maxLength={50}
              />
            </View>

            {renderColorPalette()}
          </ScrollView>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  subjectList: {
    flexDirection: 'row',
  },
  subjectChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    borderWidth: 1,
    minWidth: 80,
    maxWidth: 120,
  },
  colorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  subjectText: {
    fontSize: 14,
    flex: 1,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  manageButton: {
    marginTop: 12,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  manageButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
  },
  errorText: {
    fontSize: 12,
    marginLeft: 6,
    flex: 1,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputSection: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  colorPalette: {
    marginBottom: 24,
  },
  colorLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  colorRow: {
    flexDirection: 'row',
    paddingHorizontal: 4,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginHorizontal: 4,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColorOption: {
    borderColor: '#000',
    borderWidth: 3,
  },
  customColorSection: {
    marginTop: 16,
  },
  customColorLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  customColorInput: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    marginRight: 12,
  },
  colorPreview: {
    width: 40,
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
  },
});
