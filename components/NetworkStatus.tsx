import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Wifi, WifiOff } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';

interface NetworkStatusProps {
  showWhenOnline?: boolean;
  style?: any;
}

export default function NetworkStatus({ showWhenOnline = false, style }: NetworkStatusProps) {
  const { isOnline } = useAuth();
  const { theme } = useTheme();

  // Don't show anything if online and showWhenOnline is false
  if (isOnline && !showWhenOnline) {
    return null;
  }

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: isOnline 
          ? theme.colors.success + '20' 
          : theme.colors.error + '20',
        borderColor: isOnline 
          ? theme.colors.success 
          : theme.colors.error,
      },
      style
    ]}>
      {isOnline ? (
        <Wifi 
          size={16} 
          color={theme.colors.success} 
        />
      ) : (
        <WifiOff 
          size={16} 
          color={theme.colors.error} 
        />
      )}
      <Text style={[
        styles.text,
        { 
          color: isOnline 
            ? theme.colors.success 
            : theme.colors.error 
        }
      ]}>
        {isOnline ? 'Online' : 'Offline'}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    gap: 6,
  },
  text: {
    fontSize: 12,
    fontWeight: '500',
  },
});
