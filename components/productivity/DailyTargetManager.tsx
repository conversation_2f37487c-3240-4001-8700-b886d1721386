import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Alert,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Target,
  Plus,
  Minus,
  Check,
  X,
  Clock,
  TrendingUp,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/hooks/useAuth';
import { productivityFeaturesService, DailyTarget } from '@/services/productivityFeaturesService';
import { Card, Button } from '@/components/ui';

interface DailyTargetManagerProps {
  visible: boolean;
  onClose: () => void;
  currentTarget?: DailyTarget | null;
  onTargetUpdated?: (target: DailyTarget) => void;
}

export const DailyTargetManager: React.FC<DailyTargetManagerProps> = ({
  visible,
  onClose,
  currentTarget,
  onTargetUpdated,
}) => {
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [targetMinutes, setTargetMinutes] = useState(60); // Default 1 hour
  const [customInput, setCustomInput] = useState('');
  const [isCustomMode, setIsCustomMode] = useState(false);
  const [loading, setLoading] = useState(false);

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.9);

  // Preset target options (in minutes)
  const presetTargets = [30, 45, 60, 90, 120, 180, 240];

  useEffect(() => {
    if (visible) {
      // Set current target if available
      if (currentTarget) {
        setTargetMinutes(currentTarget.targetMinutes);
      }
      
      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.9);
    }
  }, [visible, currentTarget]);

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
  };

  const handlePresetSelect = (minutes: number) => {
    setTargetMinutes(minutes);
    setIsCustomMode(false);
    setCustomInput('');
  };

  const handleCustomInputChange = (text: string) => {
    setCustomInput(text);
    const minutes = parseInt(text);
    if (!isNaN(minutes) && minutes > 0 && minutes <= 1440) { // Max 24 hours
      setTargetMinutes(minutes);
    }
  };

  const handleIncrement = () => {
    const newValue = Math.min(targetMinutes + 15, 1440); // Max 24 hours
    setTargetMinutes(newValue);
    if (isCustomMode) {
      setCustomInput(newValue.toString());
    }
  };

  const handleDecrement = () => {
    const newValue = Math.max(targetMinutes - 15, 15); // Min 15 minutes
    setTargetMinutes(newValue);
    if (isCustomMode) {
      setCustomInput(newValue.toString());
    }
  };

  const handleSave = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      const target = await productivityFeaturesService.setDailyTarget(
        user.id,
        targetMinutes
      );
      
      onTargetUpdated?.(target);
      onClose();
      
      Alert.alert(
        'Target Set! 🎯',
        `Your daily study target is now ${formatTime(targetMinutes)}. You've got this!`,
        [{ text: 'Great!', style: 'default' }]
      );
    } catch (error) {
      console.error('Error setting daily target:', error);
      Alert.alert(
        'Error',
        'Failed to set daily target. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const getMotivationalMessage = (): string => {
    if (targetMinutes <= 30) {
      return "Perfect for getting started! 🌱";
    } else if (targetMinutes <= 60) {
      return "A solid daily commitment! 💪";
    } else if (targetMinutes <= 120) {
      return "Ambitious and achievable! 🚀";
    } else {
      return "Wow, that's dedication! 🏆";
    }
  };

  const getDifficultyColor = (): string => {
    if (targetMinutes <= 30) return theme.colors.success?.primary || theme.colors.accent.primary;
    if (targetMinutes <= 60) return theme.colors.accent.primary;
    if (targetMinutes <= 120) return theme.colors.warning?.primary || theme.colors.accent.primary;
    return theme.colors.error?.primary || theme.colors.accent.primary;
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            {
              backgroundColor: theme.colors.background.primary,
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <LinearGradient
                colors={[theme.colors.accent.primary, theme.colors.accent.secondary]}
                style={styles.iconContainer}
              >
                <Target size={24} color={theme.colors.text.inverse} />
              </LinearGradient>
              <View>
                <Text style={[styles.title, { color: theme.colors.text.primary }]}>
                  Daily Target
                </Text>
                <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
                  Set your study goal
                </Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          </View>

          {/* Current Target Display */}
          <Card style={[styles.targetDisplay, { backgroundColor: theme.colors.background.card }]}>
            <View style={styles.targetHeader}>
              <Clock size={20} color={getDifficultyColor()} />
              <Text style={[styles.targetLabel, { color: theme.colors.text.secondary }]}>
                Daily Study Target
              </Text>
            </View>
            
            <View style={styles.targetValueContainer}>
              <TouchableOpacity onPress={handleDecrement} style={styles.adjustButton}>
                <Minus size={20} color={theme.colors.accent.primary} />
              </TouchableOpacity>
              
              <Text style={[styles.targetValue, { color: theme.colors.text.primary }]}>
                {formatTime(targetMinutes)}
              </Text>
              
              <TouchableOpacity onPress={handleIncrement} style={styles.adjustButton}>
                <Plus size={20} color={theme.colors.accent.primary} />
              </TouchableOpacity>
            </View>

            <Text style={[styles.motivationalText, { color: getDifficultyColor() }]}>
              {getMotivationalMessage()}
            </Text>
          </Card>

          {/* Preset Options */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Quick Select
            </Text>
            <View style={styles.presetGrid}>
              {presetTargets.map((minutes) => (
                <TouchableOpacity
                  key={minutes}
                  style={[
                    styles.presetButton,
                    {
                      backgroundColor: targetMinutes === minutes 
                        ? theme.colors.accent.primary 
                        : theme.colors.background.card,
                      borderColor: targetMinutes === minutes
                        ? theme.colors.accent.primary
                        : theme.colors.border?.primary || theme.colors.ui.border,
                    },
                  ]}
                  onPress={() => handlePresetSelect(minutes)}
                >
                  <Text
                    style={[
                      styles.presetText,
                      {
                        color: targetMinutes === minutes 
                          ? theme.colors.text.inverse 
                          : theme.colors.text.primary,
                      },
                    ]}
                  >
                    {formatTime(minutes)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Custom Input */}
          <View style={styles.section}>
            <TouchableOpacity
              style={styles.customToggle}
              onPress={() => setIsCustomMode(!isCustomMode)}
            >
              <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
                Custom Target
              </Text>
              <TrendingUp size={16} color={theme.colors.accent.primary} />
            </TouchableOpacity>
            
            {isCustomMode && (
              <View style={styles.customInputContainer}>
                <TextInput
                  style={[
                    styles.customInput,
                    {
                      backgroundColor: theme.colors.background.card,
                      borderColor: theme.colors.border?.primary || theme.colors.ui.border,
                      color: theme.colors.text.primary,
                    },
                  ]}
                  value={customInput}
                  onChangeText={handleCustomInputChange}
                  placeholder="Enter minutes (15-1440)"
                  placeholderTextColor={theme.colors.text.tertiary}
                  keyboardType="numeric"
                  maxLength={4}
                />
                <Text style={[styles.customInputLabel, { color: theme.colors.text.secondary }]}>
                  minutes
                </Text>
              </View>
            )}
          </View>

          {/* Target Insights */}
          <Card style={[styles.insightsCard, { backgroundColor: theme.colors.background.card }]}>
            <View style={styles.insightRow}>
              <Text style={[styles.insightLabel, { color: theme.colors.text.secondary }]}>
                Weekly Goal:
              </Text>
              <Text style={[styles.insightValue, { color: theme.colors.text.primary }]}>
                {formatTime(targetMinutes * 7)}
              </Text>
            </View>
            <View style={styles.insightRow}>
              <Text style={[styles.insightLabel, { color: theme.colors.text.secondary }]}>
                Monthly Goal:
              </Text>
              <Text style={[styles.insightValue, { color: theme.colors.text.primary }]}>
                {formatTime(targetMinutes * 30)}
              </Text>
            </View>
          </Card>

          {/* Actions */}
          <View style={styles.actions}>
            <Button
              title="Cancel"
              onPress={onClose}
              variant="secondary"
              style={styles.actionButton}
            />
            <Button
              title={loading ? "Setting..." : "Set Target"}
              onPress={handleSave}
              variant="primary"
              style={[styles.actionButton, styles.primaryButton]}
              disabled={loading}
              icon={<Check size={16} color={theme.colors.text.inverse} />}
            />
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = {
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 20,
  },
  container: {
    width: '100%' as const,
    maxWidth: 400,
    borderRadius: 24,
    padding: 24,
    maxHeight: '90%' as const,
  },
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    marginBottom: 24,
  },
  headerLeft: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  title: {
    fontSize: 20,
    fontWeight: '600' as const,
  },
  subtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  closeButton: {
    padding: 8,
  },
  targetDisplay: {
    padding: 20,
    borderRadius: 16,
    alignItems: 'center' as const,
    marginBottom: 24,
  },
  targetHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    marginBottom: 16,
  },
  targetLabel: {
    fontSize: 14,
    fontWeight: '500' as const,
  },
  targetValueContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 20,
    marginBottom: 12,
  },
  adjustButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
  },
  targetValue: {
    fontSize: 36,
    fontWeight: 'bold' as const,
    minWidth: 120,
    textAlign: 'center' as const,
  },
  motivationalText: {
    fontSize: 14,
    fontWeight: '500' as const,
    fontStyle: 'italic' as const,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    marginBottom: 12,
  },
  presetGrid: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 8,
  },
  presetButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
    borderWidth: 1,
    minWidth: 70,
    alignItems: 'center' as const,
  },
  presetText: {
    fontSize: 14,
    fontWeight: '500' as const,
  },
  customToggle: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    marginBottom: 12,
  },
  customInputContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 12,
  },
  customInput: {
    flex: 1,
    height: 48,
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  customInputLabel: {
    fontSize: 14,
  },
  insightsCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  insightRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 8,
  },
  insightLabel: {
    fontSize: 14,
  },
  insightValue: {
    fontSize: 14,
    fontWeight: '600' as const,
  },
  actions: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  primaryButton: {
    flex: 2,
  },
};
