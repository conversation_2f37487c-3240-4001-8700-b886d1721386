import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  FlexAlignType,
  StyleSheet,
  ViewStyle,
  TextStyle,
  DimensionValue,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Target,
  Trophy,
  Flame,
  Star,
  TrendingUp,
  Calendar,
  Award,
  Zap,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/hooks/useAuth';
import { useAnalytics } from '@/hooks/useAnalytics';
import { productivityFeaturesService, Achievement, DailyTarget } from '@/services/productivityFeaturesService';
import { Card } from '@/components/ui';

const { width: screenWidth } = Dimensions.get('window');

interface ProductivityDashboardProps {
  onNavigateToGoals?: () => void;
  onNavigateToAnalytics?: () => void;
}

export const ProductivityDashboard: React.FC<ProductivityDashboardProps> = ({
  onNavigateToGoals,
  onNavigateToAnalytics,
}) => {
  const { theme } = useTheme();
  const { user } = useAuth();
  const { streakInfo, todayProgress, formatTime } = useAnalytics();
  
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [dailyTarget, setDailyTarget] = useState<DailyTarget | null>(null);
  const [motivationalMessage, setMotivationalMessage] = useState('');
  const [loading, setLoading] = useState(true);

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    if (user) {
      loadProductivityData();
    }
  }, [user]);

  useEffect(() => {
    // Animate in
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const loadProductivityData = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Load achievements
      const userAchievements = await productivityFeaturesService.getUserAchievements(user.id);
      setAchievements(userAchievements);

      // Check for new achievements
      await productivityFeaturesService.checkAndUpdateAchievements(user.id);

      // Load daily target
      const target = await productivityFeaturesService.getDailyTarget(user.id);
      setDailyTarget(target);

      // Get motivational message based on time of day
      const hour = new Date().getHours();
      let category: 'morning' | 'afternoon' | 'evening' = 'morning';
      if (hour >= 12 && hour < 17) category = 'afternoon';
      else if (hour >= 17) category = 'evening';

      const message = productivityFeaturesService.getMotivationalMessage(
        category,
        { streakCount: streakInfo?.currentStreak || 0 }
      );
      setMotivationalMessage(message.message);

    } catch (error) {
      console.error('Error loading productivity data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStreakColor = (streak: number): string => {
    if (streak >= 30) return '#FF6B35'; // Orange-red for 30+ days
    if (streak >= 14) return '#FF8E53'; // Orange for 2+ weeks
    if (streak >= 7) return '#FFB366'; // Light orange for 1+ week
    if (streak >= 3) return '#FFC947'; // Yellow for 3+ days
    return theme.colors.accent.primary; // Default color
  };

  const getTargetProgress = (): number => {
    if (!dailyTarget || !todayProgress) return 0;
    return Math.min((todayProgress.current / dailyTarget.targetMinutes) * 100, 100);
  };

  const getRecentAchievements = (): Achievement[] => {
    return achievements
      .filter(a => a.unlocked && a.unlockedAt)
      .sort((a, b) => {
        // The filter above ensures unlockedAt is not null
        return new Date(b.unlockedAt!).getTime() - new Date(a.unlockedAt!).getTime();
      })
      .slice(0, 3);
  };

  const getProgressAchievements = (): Achievement[] => {
    return achievements
      .filter(a => !a.unlocked && a.progress > 0)
      .sort((a, b) => {
        const aProgress = (a.progress / a.maxProgress) * 100;
        const bProgress = (b.progress / b.maxProgress) * 100;
        return bProgress - aProgress;
      })
      .slice(0, 3);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={[styles.loadingText, { color: theme.colors.text.secondary }]}>
          Loading productivity data...
        </Text>
      </View>
    );
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
        {/* Motivational Message */}
        <Card style={[styles.messageCard, { backgroundColor: theme.colors.background.card }]}>
          <LinearGradient
            colors={[theme.colors.accent.primary + '20', theme.colors.accent.secondary + '20']}
            style={styles.messageGradient}
          >
            <Star size={24} color={theme.colors.accent.primary} />
            <Text style={[styles.messageText, { color: theme.colors.text.primary }]}>
              {motivationalMessage}
            </Text>
          </LinearGradient>
        </Card>

        {/* Streak & Target Row */}
        <View style={styles.statsRow}>
          {/* Streak Card */}
          <Card style={[styles.statCard, { backgroundColor: theme.colors.background.card }]}>
            <View style={styles.statHeader}>
              <Flame size={20} color={getStreakColor(streakInfo?.currentStreak || 0)} />
              <Text style={[styles.statLabel, { color: theme.colors.text.secondary }]}>
                Study Streak
              </Text>
            </View>
            <Text style={[styles.statValue, { color: theme.colors.text.primary }]}>
              {streakInfo?.currentStreak || 0}
            </Text>
            <Text style={[styles.statSubtext, { color: theme.colors.text.secondary }]}>
              {streakInfo?.currentStreak === 1 ? 'day' : 'days'}
            </Text>
            {streakInfo?.longestStreak && streakInfo.longestStreak > (streakInfo.currentStreak || 0) && (
              <Text style={[styles.bestStreak, { color: theme.colors.text.tertiary }]}>
                Best: {streakInfo.longestStreak}
              </Text>
            )}
          </Card>

          {/* Daily Target Card */}
          <Card style={[styles.statCard, { backgroundColor: theme.colors.background.card }]}>
            <View style={styles.statHeader}>
              <Target size={20} color={theme.colors.accent.primary} />
              <Text style={[styles.statLabel, { color: theme.colors.text.secondary }]}>
                Daily Target
              </Text>
            </View>
            <Text style={[styles.statValue, { color: theme.colors.text.primary }]}>
              {todayProgress?.current || 0}m
            </Text>
            <Text style={[styles.statSubtext, { color: theme.colors.text.secondary }]}>
              of {dailyTarget?.targetMinutes || 60}m
            </Text>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${getTargetProgress()}%`,
                    backgroundColor: theme.colors.accent.primary,
                  },
                ]}
              />
            </View>
          </Card>
        </View>

        {/* Recent Achievements */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Trophy size={20} color={theme.colors.accent.primary} />
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              Recent Achievements
            </Text>
          </View>
          
          {getRecentAchievements().length > 0 ? (
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.achievementsScroll}>
              {getRecentAchievements().map((achievement) => (
                <Card key={achievement.id} style={[styles.achievementCard, { backgroundColor: theme.colors.background.card }]}>
                  <Text style={styles.achievementIcon}>{achievement.icon}</Text>
                  <Text style={[styles.achievementTitle, { color: theme.colors.text.primary }]}>
                    {achievement.title}
                  </Text>
                  <Text style={[styles.achievementDesc, { color: theme.colors.text.secondary }]}>
                    {achievement.description}
                  </Text>
                  <View style={[styles.unlockedBadge, { backgroundColor: theme.colors.success?.primary || '#28a745' }]}>
                    <Award size={12} color={theme.colors.text.inverse} />
                    <Text style={[styles.unlockedText, { color: theme.colors.text.inverse }]}>
                      Unlocked
                    </Text>
                  </View>
                </Card>
              ))}
            </ScrollView>
          ) : (
            <Card style={[styles.emptyCard, { backgroundColor: theme.colors.background.card }]}>
              <Text style={[styles.emptyText, { color: theme.colors.text.secondary }]}>
                Complete your first study session to unlock achievements! 🎯
              </Text>
            </Card>
          )}
        </View>

        {/* Progress Achievements */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <TrendingUp size={20} color={theme.colors.accent.primary} />
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
              In Progress
            </Text>
          </View>
          
          {getProgressAchievements().length > 0 ? (
            <View style={styles.progressAchievements}>
              {getProgressAchievements().map((achievement) => (
                <Card key={achievement.id} style={[styles.progressCard, { backgroundColor: theme.colors.background.card }]}>
                  <View style={styles.progressCardHeader}>
                    <Text style={styles.progressIcon}>{achievement.icon}</Text>
                    <View style={styles.progressInfo}>
                      <Text style={[styles.progressTitle, { color: theme.colors.text.primary }]}>
                        {achievement.title}
                      </Text>
                      <Text style={[styles.progressDesc, { color: theme.colors.text.secondary }]}>
                        {achievement.description}
                      </Text>
                    </View>
                    <Text style={[styles.progressPercent, { color: theme.colors.accent.primary }]}>
                      {Math.round(productivityFeaturesService.getProgressPercentage(achievement))}%
                    </Text>
                  </View>
                  <View style={styles.progressBarContainer}>
                    <View style={[styles.progressBarBg, { backgroundColor: theme.colors.background.secondary }]}>
                      <View
                        style={[
                          styles.progressBarFill,
                          {
                            width: `${productivityFeaturesService.getProgressPercentage(achievement)}%`,
                            backgroundColor: theme.colors.accent.primary,
                          },
                        ]}
                      />
                    </View>
                  </View>
                </Card>
              ))}
            </View>
          ) : (
            <Card style={[styles.emptyCard, { backgroundColor: theme.colors.background.card }]}>
              <Text style={[styles.emptyText, { color: theme.colors.text.secondary }]}>
                All achievements unlocked! Keep studying to unlock more. 🌟
              </Text>
            </Card>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <View style={styles.actionsRow}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.background.card }]}
              onPress={onNavigateToGoals}
            >
              <Calendar size={24} color={theme.colors.accent.primary} />
              <Text style={[styles.actionText, { color: theme.colors.text.primary }]}>
                Goals
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.background.card }]}
              onPress={onNavigateToAnalytics}
            >
              <Zap size={24} color={theme.colors.accent.primary} />
              <Text style={[styles.actionText, { color: theme.colors.text.primary }]}>
                Analytics
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  messageCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  messageGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 12,
  },
  messageText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  statsRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  statSubtext: {
    fontSize: 12,
  },
  bestStreak: {
    fontSize: 10,
    marginTop: 4,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginTop: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  achievementsScroll: {
    marginHorizontal: -16,
    paddingHorizontal: 16,
  },
  achievementCard: {
    width: 140,
    padding: 16,
    borderRadius: 16,
    marginRight: 12,
    alignItems: 'center',
  },
  achievementIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  achievementTitle: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  achievementDesc: {
    fontSize: 11,
    textAlign: 'center',
    marginBottom: 8,
  },
  unlockedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  unlockedText: {
    fontSize: 10,
    fontWeight: '500',
  },
  emptyCard: {
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  progressAchievements: {
    gap: 12,
  },
  progressCard: {
    padding: 16,
    borderRadius: 16,
  },
  progressCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  progressInfo: {
    flex: 1,
  },
  progressTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  progressDesc: {
    fontSize: 12,
  },
  progressPercent: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressBarContainer: {
    marginTop: 8,
  },
  progressBarBg: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 3,
  },
  actionsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    padding: 16,
    borderRadius: 16,
  },
  actionText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
