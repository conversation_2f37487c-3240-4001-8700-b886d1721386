import React, { useMemo } from 'react';
import { View, Text, TouchableOpacity, useWindowDimensions } from 'react-native';
import Animated, { useAnimatedStyle, withSpring, useSharedValue } from 'react-native-reanimated';
import { Clock, Timer } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { createDynamicIslandSelectorStyles } from './createDynamicIslandSelectorStyles';

interface DynamicIslandSelectorProps {
  options: { key: string; label: string }[];
  selectedOption: string;
  onSelectOption: (option: string) => void;
}

const DynamicIslandSelector: React.FC<DynamicIslandSelectorProps> = ({
  options,
  selectedOption,
  onSelectOption,
}) => {
  const { theme } = useTheme();
  const styles = useMemo(() => createDynamicIslandSelectorStyles(theme), [theme]);
  const { width } = useWindowDimensions();
  const numOptions = options.length;
  const selectorWidth = width - theme.spacing.lg * 2;
  const itemWidth = (selectorWidth - theme.spacing.xs * 2) / numOptions;

  const translateX = useSharedValue(0);

  React.useEffect(() => {
    const selectedIndex = options.findIndex(option => option.key === selectedOption);
    translateX.value = withSpring(selectedIndex * itemWidth, {
      damping: 15,
      stiffness: 120,
    });
  }, [selectedOption, itemWidth, options, translateX]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  const getIcon = (key: string) => {
    const isActive = selectedOption === key;
    const color = isActive ? theme.colors.interactive.button.text : theme.colors.text.secondary;
    if (key === 'stopwatch') {
      return <Clock size={20} color={color} />;
    }
    if (key === 'pomodoro') {
      return <Timer size={20} color={color} />;
    }
    return null;
  };

  return (
    <View style={[styles.container, { width: selectorWidth }]}>
      <Animated.View style={[styles.activeIndicator, { width: itemWidth }, animatedStyle]} />
      {options.map(option => (
        <TouchableOpacity
          key={option.key}
          style={[styles.optionButton, { width: itemWidth }]}
          onPress={() => onSelectOption(option.key)}
        >
          <View style={styles.optionContent}>
            {getIcon(option.key)}
            <Text style={[styles.optionText, { color: selectedOption === option.key ? theme.colors.interactive.button.text : theme.colors.text.secondary }]}>
              {option.label}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default DynamicIslandSelector;
