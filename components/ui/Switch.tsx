import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolateColor,
  runOnJS,
} from 'react-native-reanimated';
import { useTheme } from '@/contexts/ThemeContext';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export type SwitchSize = 'sm' | 'md' | 'lg';

interface SwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
  size?: SwitchSize;
  label?: string;
  description?: string;
  style?: ViewStyle;
  trackColor?: {
    false: string;
    true: string;
  };
  thumbColor?: string;
}

export const Switch: React.FC<SwitchProps> = ({
  value,
  onValueChange,
  disabled = false,
  size = 'md',
  label,
  description,
  style,
  trackColor,
  thumbColor,
}) => {
  const { theme } = useTheme();
  
  const translateX = useSharedValue(value ? 1 : 0);
  const scale = useSharedValue(1);

  React.useEffect(() => {
    translateX.value = withSpring(value ? 1 : 0, {
      damping: 15,
      stiffness: 200,
    });
  }, [value]);

  const getSizeConfig = () => {
    const sizeConfigs: Record<SwitchSize, {
      width: number;
      height: number;
      thumbSize: number;
      padding: number;
    }> = {
      sm: { width: 36, height: 20, thumbSize: 16, padding: 2 },
      md: { width: 44, height: 24, thumbSize: 20, padding: 2 },
      lg: { width: 52, height: 28, thumbSize: 24, padding: 2 },
    };
    return sizeConfigs[size];
  };

  const { width, height, thumbSize, padding } = getSizeConfig();

  const animatedTrackStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      translateX.value,
      [0, 1],
      [
        trackColor?.false || theme.colors.ui.border,
        trackColor?.true || theme.colors.accent.primary,
      ]
    );

    return {
      backgroundColor: disabled ? theme.colors.interactive.button.disabled : backgroundColor,
      transform: [{ scale: scale.value }],
    };
  });

  const animatedThumbStyle = useAnimatedStyle(() => {
    const translateDistance = width - thumbSize - padding * 2;
    
    return {
      transform: [
        { translateX: translateX.value * translateDistance },
        { scale: scale.value },
      ],
    };
  });

  const handlePress = () => {
    if (disabled) return;
    
    scale.value = withSpring(0.95, { damping: 15, stiffness: 300 }, () => {
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
    });
    
    runOnJS(onValueChange)(!value);
  };

  const trackStyle: ViewStyle = {
    width,
    height,
    borderRadius: height / 2,
    justifyContent: 'center',
    padding,
    ...theme.shadows.sm,
  };

  const thumbStyle: ViewStyle = {
    width: thumbSize,
    height: thumbSize,
    borderRadius: thumbSize / 2,
    backgroundColor: thumbColor || '#FFFFFF',
    ...theme.shadows.md,
  };

  if (label || description) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.labelContainer}>
          {label && (
            <Text style={[styles.label, { color: theme.colors.text.primary }]}>
              {label}
            </Text>
          )}
          {description && (
            <Text style={[styles.description, { color: theme.colors.text.secondary }]}>
              {description}
            </Text>
          )}
        </View>
        <AnimatedTouchableOpacity
          style={[trackStyle, animatedTrackStyle]}
          onPress={handlePress}
          activeOpacity={0.8}
          disabled={disabled}
        >
          <Animated.View style={[thumbStyle, animatedThumbStyle]} />
        </AnimatedTouchableOpacity>
      </View>
    );
  }

  return (
    <AnimatedTouchableOpacity
      style={[trackStyle, animatedTrackStyle, style]}
      onPress={handlePress}
      activeOpacity={0.8}
      disabled={disabled}
    >
      <Animated.View style={[thumbStyle, animatedThumbStyle]} />
    </AnimatedTouchableOpacity>
  );
};

// Toggle Button variant for more prominent switching
interface ToggleButtonProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  leftLabel: string;
  rightLabel: string;
  disabled?: boolean;
  style?: ViewStyle;
}

export const ToggleButton: React.FC<ToggleButtonProps> = ({
  value,
  onValueChange,
  leftLabel,
  rightLabel,
  disabled = false,
  style,
}) => {
  const { theme } = useTheme();
  const translateX = useSharedValue(value ? 1 : 0);
  const scale = useSharedValue(1);

  React.useEffect(() => {
    translateX.value = withSpring(value ? 1 : 0, {
      damping: 15,
      stiffness: 200,
    });
  }, [value]);

  const animatedIndicatorStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value * 50 }, // 50% of container width
        { scale: scale.value },
      ],
    };
  });

  const handlePress = () => {
    if (disabled) return;
    
    scale.value = withSpring(0.95, { damping: 15, stiffness: 300 }, () => {
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
    });
    
    runOnJS(onValueChange)(!value);
  };

  return (
    <TouchableOpacity
      style={[styles.toggleContainer, { backgroundColor: theme.colors.background.secondary }, style]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <Animated.View
        style={[
          styles.toggleIndicator,
          { backgroundColor: theme.colors.accent.primary },
          animatedIndicatorStyle,
        ]}
      />
      <View style={styles.toggleLabels}>
        <Text
          style={[
            styles.toggleLabel,
            {
              color: !value ? theme.colors.text.inverse : theme.colors.text.secondary,
            },
          ]}
        >
          {leftLabel}
        </Text>
        <Text
          style={[
            styles.toggleLabel,
            {
              color: value ? theme.colors.text.inverse : theme.colors.text.secondary,
            },
          ]}
        >
          {rightLabel}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  labelContainer: {
    flex: 1,
    marginRight: 16,
  },
  label: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    marginBottom: 2,
  },
  description: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  toggleContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    padding: 4,
    position: 'relative',
    height: 40,
  },
  toggleIndicator: {
    position: 'absolute',
    top: 4,
    left: 4,
    width: '50%',
    height: 32,
    borderRadius: 6,
  },
  toggleLabels: {
    flexDirection: 'row',
    flex: 1,
    zIndex: 1,
  },
  toggleLabel: {
    flex: 1,
    textAlign: 'center',
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    lineHeight: 32,
  },
});
