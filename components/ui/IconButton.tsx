import React from 'react';
import {
  TouchableOpacity,
  View,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useTheme } from '@/contexts/ThemeContext';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export type IconButtonVariant = 'primary' | 'secondary' | 'ghost' | 'danger';
export type IconButtonSize = 'sm' | 'md' | 'lg' | 'xl';

interface IconButtonProps {
  icon: React.ReactNode;
  onPress: () => void;
  variant?: IconButtonVariant;
  size?: IconButtonSize;
  disabled?: boolean;
  style?: ViewStyle;
  gradient?: boolean;
  rounded?: boolean;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onPress,
  variant = 'secondary',
  size = 'md',
  disabled = false,
  style,
  gradient = false,
  rounded = true,
}) => {
  const { theme } = useTheme();
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.9, { damping: 15, stiffness: 400 });
    opacity.value = withTiming(0.7, { duration: 100 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    opacity.value = withTiming(1, { duration: 100 });
  };

  const getButtonStyles = (): ViewStyle => {
    // Size configurations
    const sizeConfig: Record<IconButtonSize, { size: number; borderRadius: number }> = {
      sm: { size: 32, borderRadius: rounded ? 16 : theme.borderRadius.sm },
      md: { size: 44, borderRadius: rounded ? 22 : theme.borderRadius.md },
      lg: { size: 56, borderRadius: rounded ? 28 : theme.borderRadius.lg },
      xl: { size: 68, borderRadius: rounded ? 34 : theme.borderRadius.xl },
    };

    const { size: buttonSize, borderRadius } = sizeConfig[size];

    const baseStyles: ViewStyle = {
      width: buttonSize,
      height: buttonSize,
      borderRadius,
      alignItems: 'center',
      justifyContent: 'center',
      ...theme.shadows.sm,
    };

    // Variant styles
    const variantStyles: Record<IconButtonVariant, ViewStyle> = {
      primary: {
        backgroundColor: disabled ? theme.colors.interactive.button.disabled : theme.colors.interactive.button.background,
      },
      secondary: {
        backgroundColor: disabled ? theme.colors.interactive.button.disabled : theme.colors.background.secondary,
        borderWidth: 1,
        borderColor: theme.colors.ui.border,
      },
      ghost: {
        backgroundColor: 'transparent',
      },
      danger: {
        backgroundColor: disabled ? theme.colors.interactive.button.disabled : theme.colors.status.error,
      },
    };

    return {
      ...baseStyles,
      ...variantStyles[variant],
    };
  };

  if (gradient && (variant === 'primary' || variant === 'danger')) {
    const gradientColors = variant === 'primary' 
      ? theme.colors.gradients.primary 
      : [theme.colors.status.error, theme.colors.status.error];

    return (
      <AnimatedTouchableOpacity
        style={[animatedStyle, style]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={gradientColors as [string, string]}
          style={[getButtonStyles(), { backgroundColor: 'transparent' }]}
        >
          {icon}
        </LinearGradient>
      </AnimatedTouchableOpacity>
    );
  }

  return (
    <AnimatedTouchableOpacity
      style={[animatedStyle, getButtonStyles(), style]}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled}
      activeOpacity={0.8}
    >
      {icon}
    </AnimatedTouchableOpacity>
  );
};

// Floating Action Button variant
interface FloatingActionButtonProps {
  icon: React.ReactNode;
  onPress: () => void;
  size?: 'md' | 'lg';
  style?: ViewStyle;
  gradient?: boolean;
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  icon,
  onPress,
  size = 'lg',
  style,
  gradient = true,
}) => {
  const { theme } = useTheme();
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95, { damping: 15, stiffness: 300 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  const sizeConfig = {
    md: { size: 56, borderRadius: 28, shadowRadius: 8, elevation: 6 },
    lg: { size: 68, borderRadius: 24, shadowRadius: 12, elevation: 8 },
  };

  const { size: buttonSize, borderRadius, shadowRadius, elevation } = sizeConfig[size];

  const fabStyles: ViewStyle = {
    width: buttonSize,
    height: buttonSize,
    borderRadius,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: theme.colors.accent.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius,
    elevation,
  };

  if (gradient) {
    return (
      <AnimatedTouchableOpacity
        style={[animatedStyle, style]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <LinearGradient
          colors={theme.colors.gradients.primary as readonly [string, string]}
          style={fabStyles}
        >
          {icon}
        </LinearGradient>
      </AnimatedTouchableOpacity>
    );
  }

  return (
    <AnimatedTouchableOpacity
      style={[animatedStyle, fabStyles, { backgroundColor: theme.colors.accent.primary }, style]}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.9}
    >
      {icon}
    </AnimatedTouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Add any additional styles if needed
});
