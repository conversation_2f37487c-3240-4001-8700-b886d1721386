import { StyleSheet } from 'react-native';
import { Theme } from '@/types/theme';

export const createDynamicIslandSelectorStyles = (theme: Theme) => {
  const { colors, spacing, borderRadius } = theme;

  return StyleSheet.create({
    container: {
      flexDirection: 'row',
      backgroundColor: colors.background.secondary,
      borderRadius: borderRadius.full,
      height: 56,
      padding: spacing.xs,
      alignSelf: 'center',
      marginVertical: spacing.lg,
    },
    activeIndicator: {
      position: 'absolute',
      height: '100%',
      backgroundColor: colors.accent.primary,
      borderRadius: borderRadius.full - spacing.xs,
      top: spacing.xs,
      left: spacing.xs,
    },
    optionButton: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    optionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.sm,
    },
    optionText: {
      fontSize: 16,
      fontWeight: '600',
    },
  });
};
