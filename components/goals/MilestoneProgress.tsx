import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { Award } from 'lucide-react-native';
import { Task } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';

interface MilestoneProgressProps {
  tasks: Task[];
  screenWidth: number;
}

const MilestoneProgress: React.FC<MilestoneProgressProps> = ({ tasks, screenWidth }) => {
  const { theme } = useTheme(); // Access theme here
  const localScale = (size: number) => (screenWidth / 375) * size;
  const milestones = tasks.filter(task => task.is_milestone);
  const completedMilestones = milestones.filter(task => task.status === 'completed');
  const progressPercentage = milestones.length > 0 ? (completedMilestones.length / milestones.length) * 100 : 0;

  const animatedProgress = useSharedValue(0);

  useEffect(() => {
    animatedProgress.value = withTiming(progressPercentage, { duration: 1000 });
  }, [progressPercentage]);

  const animatedStyle = useAnimatedStyle(() => ({
    width: `${animatedProgress.value}%`,
  }));

  return (
    <View style={styles(screenWidth, theme).milestoneProgress}>
      <View style={styles(screenWidth, theme).milestoneHeader}>
        <Award size={localScale(20)} color="#F59E0B" />
        <Text style={styles(screenWidth, theme).milestoneTitle}>Milestone Progress</Text>
      </View>
      <View style={styles(screenWidth, theme).milestoneBar}>
        <Animated.View style={[styles(screenWidth, theme).milestoneBarFill, animatedStyle]} />
      </View>
      <View style={styles(screenWidth, theme).milestoneStats}>
        <Text style={styles(screenWidth, theme).milestoneStatsText}>
          {completedMilestones.length} of {milestones.length} milestones completed
        </Text>
        <Text style={styles(screenWidth, theme).milestonePercentage}>{Math.round(progressPercentage)}%</Text>
      </View>
    </View>
  );
};

const styles = (screenWidth: number, theme: any) => { // Pass theme to styles function
  const localScale = (size: number) => (screenWidth / 375) * size;
  return StyleSheet.create({
    milestoneProgress: {
      backgroundColor: theme.colors.background.card,
      borderRadius: localScale(16),
      padding: localScale(20),
      marginHorizontal: localScale(16),
      marginBottom: localScale(16),
      ...theme.shadows.md,
    },
    milestoneHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: localScale(16),
      gap: localScale(8),
    },
    milestoneTitle: {
      fontSize: localScale(18),
      fontWeight: '600',
      color: theme.colors.text.primary,
    },
    milestoneBar: {
      height: localScale(12),
      backgroundColor: theme.colors.ui.border,
      borderRadius: localScale(6),
      overflow: 'hidden',
      marginBottom: localScale(12),
    },
    milestoneBarFill: {
      height: '100%',
      backgroundColor: theme.colors.status.warning,
      borderRadius: localScale(6),
    },
    milestoneStats: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    milestoneStatsText: {
      fontSize: localScale(14),
      color: theme.colors.text.secondary,
    },
    milestonePercentage: {
      fontSize: localScale(16),
      fontWeight: '600',
      color: theme.colors.status.warning,
    },
  });
};

export default MilestoneProgress;
