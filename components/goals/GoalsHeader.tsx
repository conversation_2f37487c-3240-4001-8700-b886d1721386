import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import Animated from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Plus,
  Filter,
  BarChart3,
  Search,
} from 'lucide-react-native';
import IsotopeLogo from '@/components/IsotopeLogo';
import { Task } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';
import { IconButton, FloatingActionButton, Input } from '@/components/ui';

interface GoalsHeaderProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  showStats: boolean;
  setShowStats: (show: boolean) => void;
  selectedView: 'all' | 'todo' | 'in_progress' | 'completed';
  setSelectedView: (view: 'all' | 'todo' | 'in_progress' | 'completed') => void;
  onAddTask: () => void;
  onOpenFilter: () => void;
  tasks: Task[];
  screenWidth: number;
}

const GoalsHeader: React.FC<GoalsHeaderProps> = ({
  searchQuery,
  setSearchQuery,
  showStats,
  setShowStats,
  selectedView,
  setSelectedView,
  onAddTask,
  onOpenFilter,
  tasks,
  screenWidth, // Destructure screenWidth
}) => {
  const { theme } = useTheme(); // Access theme here
  const todoTasks = tasks.filter(task => task.status === 'todo');
  const inProgressTasks = tasks.filter(task => task.status === 'in_progress');
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const overdueTasks = tasks.filter(task => 
    task.due_date && 
    task.due_date < new Date() && 
    task.due_date.toDateString() !== new Date().toDateString()
  );

  const localScale = (size: number) => scale(size, screenWidth);

  return (
    <LinearGradient
      colors={[theme.colors.background.primary, theme.colors.background.secondary, theme.colors.background.primary]}
      style={styles(screenWidth).headerGradient}
    >
      <View style={styles(screenWidth).header}>
        <View style={styles(screenWidth).headerLeft}>
          <IsotopeLogo size="medium" />
          <Text numberOfLines={1} style={[styles(screenWidth).subtitle, { color: theme.colors.text.secondary }]}>Task Management</Text>
        </View>
        <View style={styles(screenWidth).headerRight}>
          <IconButton
            icon={<BarChart3 size={localScale(20)} color={theme.colors.accent.primary} />}
            onPress={() => setShowStats(!showStats)}
            variant="secondary"
            size="md"
          />
          <FloatingActionButton
            icon={<Plus size={localScale(20)} color={theme.colors.text.inverse} />}
            onPress={onAddTask}
            size="md"
          />
        </View>
      </View>

      {/* Quick Stats and Filter Tabs (Hidden when showStats is true) */}
      {!showStats && (
        <>
          <Animated.View style={styles(screenWidth).statsContainer}>
            <View style={[styles(screenWidth).statCard, { backgroundColor: theme.colors.background.card, ...theme.shadows.md }]}>
              <Text style={[styles(screenWidth).statNumber, { color: theme.colors.text.primary }]}>{todoTasks.length}</Text>
              <Text style={[styles(screenWidth).statLabel, { color: theme.colors.text.secondary }]}>To Do</Text>
            </View>
            <View style={[styles(screenWidth).statCard, { backgroundColor: theme.colors.background.card, ...theme.shadows.md }]}>
              <Text style={[styles(screenWidth).statNumber, { color: theme.colors.text.primary }]}>{inProgressTasks.length}</Text>
              <Text style={[styles(screenWidth).statLabel, { color: theme.colors.text.secondary }]}>In Progress</Text>
            </View>
            <View style={[styles(screenWidth).statCard, { backgroundColor: theme.colors.background.card, ...theme.shadows.md }]}>
              <Text style={[styles(screenWidth).statNumber, { color: theme.colors.text.primary }]}>{completedTasks.length}</Text>
              <Text style={[styles(screenWidth).statLabel, { color: theme.colors.text.secondary }]}>Completed</Text>
            </View>
            <View style={[styles(screenWidth).statCard, { backgroundColor: theme.colors.background.card, ...theme.shadows.md }]}>
              <Text style={[styles(screenWidth).statNumber, { color: theme.colors.status.error }]}>{overdueTasks.length}</Text>
              <Text style={[styles(screenWidth).statLabel, { color: theme.colors.text.secondary }]}>Overdue</Text>
            </View>
          </Animated.View>

          {/* Filter Tabs */}
          <View style={styles(screenWidth).filterTabs}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles(screenWidth).tabsContainer}>
              {['all', 'todo', 'in_progress', 'completed'].map((view) => (
                <TouchableOpacity
                  key={view}
                  style={[
                    styles(screenWidth).filterTab,
                    { backgroundColor: theme.colors.background.secondary, borderColor: theme.colors.ui.border },
                    selectedView === view && { backgroundColor: theme.colors.accent.primary, borderColor: theme.colors.accent.primary }
                  ]}
                  onPress={() => setSelectedView(view as any)}
                >
                  <Text style={[
                    styles(screenWidth).filterTabText,
                    { color: theme.colors.text.secondary },
                    selectedView === view && { color: theme.colors.text.inverse }
                  ]}>
                    {view === 'all' ? 'All' :
                     view === 'todo' ? 'To Do' :
                     view === 'in_progress' ? 'In Progress' : 'Completed'}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </>
      )}

    </LinearGradient>
  );
};

const scale = (size: number, screenWidth: number) => (screenWidth / 375) * size;

const styles = (screenWidth: number) => StyleSheet.create({
  headerGradient: {
    paddingTop: scale(60, screenWidth),
    paddingBottom: scale(20, screenWidth),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(24, screenWidth),
  },
  subtitle: {
    fontSize: scale(14, screenWidth),
    fontFamily: 'Inter-Regular',
    marginTop: scale(4, screenWidth),
    // Color applied inline in the component
  },
  headerLeft: {
    flex: 1,
    marginRight: scale(16, screenWidth),
    overflow: 'hidden',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(12, screenWidth),
  },
  statsButton: {
    width: scale(40, screenWidth),
    height: scale(40, screenWidth),
    borderRadius: scale(20, screenWidth),
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterButton: {
    width: scale(40, screenWidth),
    height: scale(40, screenWidth),
    borderRadius: scale(20, screenWidth),
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButton: {
    width: scale(44, screenWidth),
    height: scale(44, screenWidth),
    borderRadius: scale(22, screenWidth),
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: { width: 0, height: scale(4, screenWidth) },
    shadowOpacity: 0.3,
    shadowRadius: scale(8, screenWidth),
    elevation: scale(6, screenWidth),
  },
  searchContainer: {
    paddingHorizontal: scale(20, screenWidth),
    paddingTop: scale(16, screenWidth),
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: scale(12, screenWidth),
    paddingHorizontal: scale(16, screenWidth),
    paddingVertical: scale(12, screenWidth),
    // Background and shadow applied inline
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap', // Allow items to wrap to the next line
    justifyContent: 'space-between', // Distribute items evenly
    paddingHorizontal: scale(20, screenWidth),
    paddingTop: scale(16, screenWidth),
    gap: scale(12, screenWidth), // Use gap for spacing between items
  },
  statCard: {
    width: (screenWidth - scale(40, screenWidth) - scale(12, screenWidth)) / 2, // Two cards per row with spacing
    minWidth: scale(150, screenWidth), // Minimum width for larger screens
    borderRadius: scale(12, screenWidth),
    padding: scale(16, screenWidth),
    alignItems: 'center',
    // Background and shadow applied inline
  },
  statNumber: {
    fontSize: scale(24, screenWidth),
    fontWeight: 'bold',
    // Color applied inline
  },
  statLabel: {
    fontSize: scale(12, screenWidth),
    marginTop: scale(4, screenWidth),
    // Color applied inline
  },
  filterTabs: {
    paddingTop: scale(16, screenWidth),
  },
  tabsContainer: {
    paddingHorizontal: scale(20, screenWidth),
  },
  filterTab: {
    paddingHorizontal: scale(20, screenWidth),
    paddingVertical: scale(8, screenWidth),
    marginRight: scale(12, screenWidth),
    borderRadius: scale(20, screenWidth),
    borderWidth: scale(1, screenWidth),
    // Background and border color applied inline
  },
  filterTabText: {
    fontSize: scale(14, screenWidth),
    fontWeight: '500',
    // Color applied inline
  },
});

export default GoalsHeader;
