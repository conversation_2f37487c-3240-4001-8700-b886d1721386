import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
} from 'react-native-reanimated';
import { X } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';

interface FilterModalProps {
  visible: boolean;
  selectedView: 'all' | 'todo' | 'in_progress' | 'completed';
  setSelectedView: (view: 'all' | 'todo' | 'in_progress' | 'completed') => void;
  onClose: () => void;
  filterModalScale: Animated.SharedValue<number>;
  screenWidth: number;
}

export const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  selectedView,
  setSelectedView,
  onClose,
  filterModalScale,
  screenWidth,
}) => {
  const { theme } = useTheme();

  const filterModalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: filterModalScale.value }],
  }));

  // Define localScale here to be used within the component's render logic
  const localScale = (size: number) => (screenWidth / 375) * size;

  // Create themed styles
  const modalStyles = styles(screenWidth, theme);

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={modalStyles.modalOverlay}>
        <Animated.View style={[modalStyles.filterModalContent, filterModalAnimatedStyle]}>
          <View style={modalStyles.modalHeader}>
            <Text style={modalStyles.modalTitle}>Filter Tasks</Text>
            <TouchableOpacity onPress={onClose}>
              <X size={localScale(24)} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          </View>

          <ScrollView style={modalStyles.modalBody}>
            {/* Status Filter */}
            <View style={modalStyles.filterSection}>
              <Text style={modalStyles.filterSectionTitle}>Status</Text>
              <View style={modalStyles.filterOptions}>
                {['all', 'todo', 'in_progress', 'completed'].map((status) => (
                  <TouchableOpacity
                    key={status}
                    style={[
                      modalStyles.filterOption,
                      selectedView === status && modalStyles.filterOptionActive
                    ]}
                    onPress={() => setSelectedView(status as any)}
                  >
                    <Text style={[
                      modalStyles.filterOptionText,
                      selectedView === status && modalStyles.filterOptionActiveText
                    ]}>
                      {status === 'all' ? 'All' :
                       status === 'todo' ? 'To Do' :
                       status === 'in_progress' ? 'In Progress' : 'Completed'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          <View style={modalStyles.modalActions}>
            <TouchableOpacity style={modalStyles.cancelButton} onPress={onClose}>
              <Text style={modalStyles.cancelText}>Close</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = (screenWidth: number, theme: any) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  return StyleSheet.create({
    modalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.ui.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    padding: localScale(20),
  },
  filterModalContent: {
    backgroundColor: theme.colors.background.card,
    borderRadius: localScale(20),
    width: '100%',
    maxHeight: '70%',
    shadowColor: theme.colors.ui.shadow,
    shadowOffset: { width: 0, height: localScale(10) },
    shadowOpacity: 0.25,
    shadowRadius: localScale(20),
    elevation: localScale(10),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: localScale(20),
    borderBottomWidth: localScale(1),
    borderBottomColor: '#F3F4F6',
  },
  modalTitle: {
    fontSize: localScale(20),
    fontWeight: '600',
    color: '#1F2937',
  },
  modalBody: {
    padding: localScale(20),
  },
  filterSection: {
    marginBottom: localScale(24),
  },
  filterSectionTitle: {
    fontSize: localScale(18),
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: localScale(12),
  },
  filterOptions: {
    gap: localScale(8),
  },
  filterOption: {
    paddingVertical: localScale(12),
    paddingHorizontal: localScale(16),
    borderRadius: localScale(12),
    backgroundColor: '#F9FAFB',
    borderWidth: localScale(1),
    borderColor: '#E5E7EB',
  },
  filterOptionActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  filterOptionText: {
    fontSize: localScale(16),
    color: '#374151',
    fontWeight: '500',
  },
  filterOptionActiveText: {
    color: '#FFFFFF',
  },
  modalActions: {
    padding: localScale(20),
    borderTopWidth: localScale(1),
    borderTopColor: '#F3F4F6',
  },
  cancelButton: {
    paddingVertical: localScale(12),
    paddingHorizontal: localScale(24),
    borderRadius: localScale(12),
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
  },
  cancelText: {
    fontSize: localScale(16),
    color: '#6B7280',
    fontWeight: '500',
  },
});

}
