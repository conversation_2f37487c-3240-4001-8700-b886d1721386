import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  X,
  Calendar as CalendarIcon,
  Clock,
  CheckCircle,
  Star,
  Bell,
  Trash2,
} from 'lucide-react-native';
import SubjectPicker from '@/components/SubjectPicker';
import { Task } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';

interface TaskModalProps {
  visible: boolean;
  editingTask: Task | null;
  title: string;
  setTitle: (title: string) => void;
  description: string;
  setDescription: (description: string) => void;
  dueDate: Date | undefined;
  setDueDate: (date: Date | undefined) => void;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  setPriority: (priority: 'low' | 'medium' | 'high' | 'urgent') => void;
  reminderEnabled: boolean;
  setReminderEnabled: (enabled: boolean) => void;
  reminderTime: Date | undefined;
  setReminderTime: (time: Date | undefined) => void;
  periodicReminders: boolean;
  setPeriodicReminders: (enabled: boolean) => void;
  reminderInterval: 'daily' | 'weekly' | 'monthly';
  setReminderInterval: (interval: 'daily' | 'weekly' | 'monthly') => void;
  selectedSubject: any;
  setSelectedSubject: (subject: any) => void;
  isMilestone: boolean;
  setIsMilestone: (milestone: boolean) => void;
  onClose: () => void;
  onSave: () => void;
  onDelete?: () => void;
  getPriorityColor: (priority: 'low' | 'medium' | 'high' | 'urgent') => string;
  getPriorityIcon: (priority: 'low' | 'medium' | 'high' | 'urgent') => React.ReactNode;
  modalScale: Animated.SharedValue<number>;
  screenWidth: number;
}

export const TaskModal: React.FC<TaskModalProps> = ({
  visible,
  editingTask,
  title,
  setTitle,
  description,
  setDescription,
  dueDate,
  setDueDate,
  priority,
  setPriority,
  reminderEnabled,
  setReminderEnabled,
  reminderTime,
  setReminderTime,
  periodicReminders,
  setPeriodicReminders,
  reminderInterval,
  setReminderInterval,
  selectedSubject,
  setSelectedSubject,
  isMilestone,
  setIsMilestone,
  onClose,
  onSave,
  onDelete,
  getPriorityColor,
  getPriorityIcon,
  modalScale,
  screenWidth,
}) => {
  const { theme } = useTheme();

  // Date/Time picker states
  const [showDueDatePicker, setShowDueDatePicker] = useState(false);
  const [showReminderDatePicker, setShowReminderDatePicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);

  const modalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modalScale.value }],
  }));

  // Define localScale here to be used within the component's render logic
  const localScale = (size: number) => (screenWidth / 375) * size;

  // Create themed styles
  const modalStyles = styles(screenWidth, theme);

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={modalStyles.modalOverlay}>
        <Animated.View style={[modalStyles.modalContent, modalAnimatedStyle]}>
          <View style={modalStyles.modalHeader}>
            <Text style={modalStyles.modalTitle}>
              {editingTask ? 'Edit Task' : 'Create New Task'}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <X size={localScale(24)} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          </View>

          <ScrollView style={modalStyles.modalBody} showsVerticalScrollIndicator={false}>
            {/* Task Title */}
            <View style={modalStyles.inputGroup}>
              <Text style={modalStyles.inputLabel}>Task Title *</Text>
              <TextInput
                style={modalStyles.textInput}
                value={title}
                onChangeText={setTitle}
                placeholder="Enter task title"
                placeholderTextColor={theme.colors.interactive.input.placeholder}
              />
            </View>

            {/* Description */}
            <View style={modalStyles.inputGroup}>
              <Text style={modalStyles.inputLabel}>Description</Text>
              <TextInput
                style={[modalStyles.textInput, modalStyles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Enter task description"
                placeholderTextColor={theme.colors.interactive.input.placeholder}
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={modalStyles.inputGroup}>
              <Text style={modalStyles.inputLabel}>Due Date</Text>
              <TouchableOpacity
                style={modalStyles.datePickerButton}
                onPress={() => setShowDueDatePicker(true)}
              >
                <CalendarIcon size={localScale(20)} color={theme.colors.text.secondary} />
                <Text style={modalStyles.datePickerText}>
                  {dueDate ? dueDate.toLocaleDateString() : 'Select due date'}
                </Text>
              </TouchableOpacity>
              {showDueDatePicker && (
                <DateTimePicker
                  value={dueDate || new Date()}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  textColor={theme.colors.text.primary}
                  onChange={(event, selectedDate) => {
                    setShowDueDatePicker(false);
                    if (selectedDate) {
                      setDueDate(selectedDate);
                    }
                  }}
                />
              )}
            </View>

            {/* Priority */}
            <View style={modalStyles.inputGroup}>
              <Text style={modalStyles.inputLabel}>Priority</Text>
              <View style={modalStyles.prioritySelector}>
                {(['low', 'medium', 'high', 'urgent'] as const).map((p) => (
                  <TouchableOpacity
                    key={p}
                    style={[
                      modalStyles.priorityButton,
                      priority === p && modalStyles.priorityButtonActive,
                      { borderColor: getPriorityColor(p) },
                    ]}
                    onPress={() => setPriority(p)}
                  >
                    {getPriorityIcon(p)}
                    <Text
                      style={[
                        modalStyles.priorityButtonText,
                        priority === p && { color: getPriorityColor(p) },
                      ]}
                    >
                      {p.charAt(0).toUpperCase() + p.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Milestone Toggle */}
            <View style={modalStyles.inputGroup}>
              <TouchableOpacity
                style={modalStyles.checkboxRow}
                onPress={() => setIsMilestone(!isMilestone)}
              >
                <View style={[modalStyles.checkbox, isMilestone && modalStyles.checkboxActive]}>
                  {isMilestone && <CheckCircle size={localScale(16)} color={theme.colors.text.inverse} />}
                </View>
                <Text style={modalStyles.checkboxLabel}>Mark as milestone</Text>
                <Star size={localScale(16)} color={theme.colors.status.warning} />
              </TouchableOpacity>
            </View>

            {/* Reminder Settings */}
            <View style={modalStyles.inputGroup}>
              <TouchableOpacity
                style={modalStyles.checkboxRow}
                onPress={() => setReminderEnabled(!reminderEnabled)}
              >
                <View style={[modalStyles.checkbox, reminderEnabled && modalStyles.checkboxActive]}>
                  {reminderEnabled && <CheckCircle size={localScale(16)} color={theme.colors.text.inverse} />}
                </View>
                <Text style={modalStyles.checkboxLabel}>Enable reminders</Text>
                <Bell size={localScale(16)} color={theme.colors.status.info} />
              </TouchableOpacity>

              {reminderEnabled && (
                <View style={modalStyles.reminderTimeContainer}>
                  <Text style={modalStyles.reminderTimeLabel}>Reminder Date & Time</Text>

                  {/* Reminder Date */}
                  <TouchableOpacity
                    style={modalStyles.textInput}
                    onPress={() => setShowReminderDatePicker(true)}
                  >
                    <View style={modalStyles.dateTimePickerRow}>
                      <CalendarIcon size={localScale(16)} color={theme.colors.text.secondary} />
                      <Text style={modalStyles.dateTimePickerText}>
                        {reminderTime ? reminderTime.toLocaleDateString() : 'Select date'}
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {/* Reminder Time */}
                  <TouchableOpacity
                    style={modalStyles.textInput}
                    onPress={() => setShowReminderTimePicker(true)}
                  >
                    <View style={modalStyles.dateTimePickerRow}>
                      <Clock size={localScale(16)} color={theme.colors.text.secondary} />
                      <Text style={modalStyles.dateTimePickerText}>
                        {reminderTime ? reminderTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'Select time'}
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {/* Periodic Reminders */}
                  <TouchableOpacity
                    style={modalStyles.checkboxRow}
                    onPress={() => setPeriodicReminders(!periodicReminders)}
                  >
                    <View style={[modalStyles.checkbox, periodicReminders && modalStyles.checkboxActive]}>
                      {periodicReminders && <CheckCircle size={localScale(16)} color={theme.colors.text.inverse} />}
                    </View>
                    <Text style={modalStyles.checkboxLabel}>Repeat reminders</Text>
                  </TouchableOpacity>

                  {periodicReminders && (
                    <View style={modalStyles.reminderIntervalContainer}>
                      <Text style={modalStyles.reminderIntervalLabel}>Repeat every:</Text>
                      <View style={modalStyles.intervalSelector}>
                        {(['daily', 'weekly', 'monthly'] as const).map((interval) => (
                          <TouchableOpacity
                            key={interval}
                            style={[
                              modalStyles.intervalButton,
                              reminderInterval === interval && modalStyles.intervalButtonActive
                            ]}
                            onPress={() => setReminderInterval(interval)}
                          >
                            <Text style={[
                              modalStyles.intervalButtonText,
                              reminderInterval === interval && modalStyles.intervalButtonActiveText
                            ]}>
                              {interval.charAt(0).toUpperCase() + interval.slice(1)}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  )}

                  {/* Date/Time Pickers */}
                  {showReminderDatePicker && (
                    <DateTimePicker
                      value={reminderTime || new Date()}
                      mode="date"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      textColor={theme.colors.text.primary}
                      onChange={(_, selectedDate) => {
                        setShowReminderDatePicker(false);
                        if (selectedDate) {
                          const newDateTime = reminderTime ? new Date(reminderTime) : new Date();
                          newDateTime.setFullYear(selectedDate.getFullYear());
                          newDateTime.setMonth(selectedDate.getMonth());
                          newDateTime.setDate(selectedDate.getDate());
                          setReminderTime(newDateTime);
                        }
                      }}
                    />
                  )}

                  {showReminderTimePicker && (
                    <DateTimePicker
                      value={reminderTime || new Date()}
                      mode="time"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      textColor={theme.colors.text.primary}
                      onChange={(_, selectedTime) => {
                        setShowReminderTimePicker(false);
                        if (selectedTime) {
                          const newDateTime = reminderTime ? new Date(reminderTime) : new Date();
                          newDateTime.setHours(selectedTime.getHours());
                          newDateTime.setMinutes(selectedTime.getMinutes());
                          setReminderTime(newDateTime);
                        }
                      }}
                    />
                  )}

                  <Text style={modalStyles.reminderHint}>
                    Set when you want to be reminded about this task
                  </Text>
                </View>
              )}
            </View>

            {/* Subject */}
            <View style={modalStyles.inputGroup}>
              <SubjectPicker
                selectedSubject={selectedSubject}
                onSelectSubject={setSelectedSubject}
              />
            </View>
          </ScrollView>
          
          <View style={modalStyles.modalActions}>
            {editingTask && onDelete && (
              <TouchableOpacity
                style={modalStyles.deleteButton}
                onPress={() => {
                  onClose();
                  onDelete();
                }}
              >
                <Trash2 size={localScale(16)} color={theme.colors.status.error} />
                <Text style={modalStyles.deleteText}>Delete</Text>
              </TouchableOpacity>
            )}

            <View style={modalStyles.actionButtons}>
              <TouchableOpacity style={modalStyles.cancelButton} onPress={onClose}>
                <Text style={modalStyles.cancelText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity style={modalStyles.saveButton} onPress={onSave}>
                <LinearGradient
                  colors={theme.colors.gradients.primary}
                  style={modalStyles.saveGradient}
                >
                  <Text style={modalStyles.saveText}>
                    {editingTask ? 'Update' : 'Create'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = (screenWidth: number, theme: any) => {
  const localScale = (size: number) => (screenWidth / 375) * size; // Moved here
  return StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.ui.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.md,
  },
  modalContent: {
    backgroundColor: theme.colors.background.card,
    borderRadius: theme.borderRadius.lg,
    width: '100%',
    maxHeight: '90%',
    ...theme.shadows.lg,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.md,
    borderBottomWidth: localScale(1),
    borderBottomColor: theme.colors.ui.border,
  },
  modalTitle: {
    fontSize: localScale(20),
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  modalBody: {
    padding: theme.spacing.md,
    maxHeight: localScale(400),
  },
  inputGroup: {
    marginBottom: theme.spacing.md,
  },
  inputLabel: {
    fontSize: localScale(16),
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  textInput: {
    borderWidth: localScale(1),
    borderColor: theme.colors.interactive.input.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: localScale(16),
    color: theme.colors.text.primary,
    backgroundColor: theme.colors.interactive.input.background,
  },
  textArea: {
    height: localScale(80),
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: localScale(1),
    borderColor: theme.colors.interactive.input.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.interactive.input.background,
    gap: theme.spacing.sm,
  },
  datePickerText: {
    fontSize: localScale(16),
    color: theme.colors.text.primary,
  },
  prioritySelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.xs,
  },
  priorityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.sm, /* Reduced padding to allow more text space */
    borderWidth: localScale(2),
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.interactive.card.background,
    gap: theme.spacing.xs,
  },
  priorityButtonActive: {
    backgroundColor: theme.colors.interactive.card.hover,
  },
  priorityButtonText: {
    fontSize: localScale(14),
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  checkbox: {
    width: localScale(20),
    height: localScale(20),
    borderWidth: localScale(2),
    borderColor: theme.colors.interactive.input.border,
    borderRadius: theme.borderRadius.sm,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.interactive.input.background,
  },
  checkboxActive: {
    backgroundColor: theme.colors.accent.secondary,
    borderColor: theme.colors.accent.secondary,
  },
  checkboxLabel: {
    fontSize: localScale(16),
    color: theme.colors.text.primary,
    flex: 1,
  },
  reminderTimeContainer: {
    marginTop: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  reminderTimeLabel: {
    fontSize: localScale(14),
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  dateTimePickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  dateTimePickerText: {
    fontSize: localScale(16),
    color: theme.colors.text.primary,
  },
  reminderIntervalContainer: {
    marginTop: theme.spacing.md,
    gap: theme.spacing.xs,
  },
  reminderIntervalLabel: {
    fontSize: localScale(14),
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  intervalSelector: {
    flexDirection: 'row',
    gap: theme.spacing.xs,
  },
  intervalButton: {
    flex: 1,
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    borderWidth: localScale(1),
    borderColor: theme.colors.interactive.input.border,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: theme.colors.interactive.input.background,
    alignItems: 'center',
  },
  intervalButtonActive: {
    backgroundColor: theme.colors.accent.secondary,
    borderColor: theme.colors.accent.secondary,
  },
  intervalButtonText: {
    fontSize: localScale(14),
    color: theme.colors.text.secondary,
    fontWeight: '500',
  },
  intervalButtonActiveText: {
    color: theme.colors.text.inverse,
  },
  reminderHint: {
    fontSize: localScale(12),
    color: theme.colors.text.disabled,
    fontStyle: 'italic',
    marginTop: theme.spacing.xs,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.md,
    borderTopWidth: localScale(1),
    borderTopColor: theme.colors.ui.border,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  },
  deleteText: {
    fontSize: localScale(16),
    color: theme.colors.status.error,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  cancelButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.interactive.button.disabled,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: localScale(16),
    color: theme.colors.text.secondary,
    fontWeight: '500',
  },
  saveButton: {
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
  },
  saveGradient: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.lg,
    alignItems: 'center',
  },
  saveText: {
    fontSize: localScale(16),
    color: theme.colors.interactive.button.text,
    fontWeight: '600',
  },
});
}
