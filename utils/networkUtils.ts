import { Platform } from 'react-native';

export interface NetworkError extends Error {
  code?: string;
  status?: number;
  isNetworkError?: boolean;
}

export interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  retryCondition?: (error: Error) => boolean;
}

/**
 * Check if an error is a network-related error
 */
export const isNetworkError = (error: any): boolean => {
  if (!error) return false;
  
  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code?.toLowerCase() || '';
  
  // Common network error patterns
  const networkErrorPatterns = [
    'network request failed',
    'network error',
    'connection failed',
    'timeout',
    'fetch failed',
    'unable to connect',
    'connection refused',
    'connection reset',
    'no internet',
    'offline',
    'dns',
    'unreachable',
  ];
  
  // Check error codes
  const networkErrorCodes = [
    'network_error',
    'timeout_error',
    'connection_error',
    'fetch_error',
    'enotfound',
    'econnrefused',
    'econnreset',
    'etimedout',
  ];
  
  return (
    networkErrorPatterns.some(pattern => errorMessage.includes(pattern)) ||
    networkErrorCodes.some(code => errorCode.includes(code)) ||
    error.name === 'TypeError' && errorMessage.includes('failed to fetch') ||
    error.name === 'AbortError' ||
    (error.status >= 500 && error.status < 600) // Server errors
  );
};

/**
 * Enhanced retry function with exponential backoff
 */
export const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    retryCondition = isNetworkError,
  } = options;

  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      console.log(`Operation failed (attempt ${attempt}/${maxRetries}):`, error);
      
      // Don't retry if condition is not met
      if (!retryCondition(lastError)) {
        throw lastError;
      }
      
      // Don't wait after the last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff and jitter
      const exponentialDelay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
      const jitter = Math.random() * 0.1 * exponentialDelay; // Add up to 10% jitter
      const delay = exponentialDelay + jitter;
      
      console.log(`Retrying in ${Math.round(delay)}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
};

/**
 * Enhanced fetch with timeout and retry
 */
export const enhancedFetch = async (
  url: RequestInfo | URL,
  options: RequestInit & { timeout?: number; retries?: number } = {}
): Promise<Response> => {
  const { timeout = 30000, retries = 3, ...fetchOptions } = options;
  
  return retryWithBackoff(
    async () => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      try {
        const response = await fetch(url, {
          ...fetchOptions,
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': Platform.OS === 'ios' ? 'IsotopeAI-iOS' : 'IsotopeAI-Android',
            ...fetchOptions.headers,
          },
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return response;
      } finally {
        clearTimeout(timeoutId);
      }
    },
    { maxRetries: retries }
  );
};

/**
 * Get user-friendly error message for network errors
 */
export const getNetworkErrorMessage = (error: any): string => {
  if (!error) return 'An unknown error occurred';
  
  const errorMessage = error.message?.toLowerCase() || '';
  
  if (errorMessage.includes('network request failed')) {
    return 'Network connection failed. Please check your internet connection and try again.';
  }
  
  if (errorMessage.includes('timeout')) {
    return 'Request timed out. Please check your internet connection and try again.';
  }
  
  if (errorMessage.includes('offline') || errorMessage.includes('no internet')) {
    return 'You appear to be offline. Please check your internet connection.';
  }
  
  if (errorMessage.includes('dns') || errorMessage.includes('unreachable')) {
    return 'Unable to reach the server. Please check your internet connection.';
  }
  
  if (error.status >= 500) {
    return 'Server error. Please try again later.';
  }
  
  if (error.status === 429) {
    return 'Too many requests. Please wait a moment and try again.';
  }
  
  // Return original message for non-network errors
  return error.message || 'An unexpected error occurred';
};

/**
 * Check network connectivity (basic check)
 */
export const checkNetworkConnectivity = async (): Promise<boolean> => {
  try {
    const response = await fetch('https://www.google.com/generate_204', {
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'no-cache',
    });
    return true;
  } catch {
    return false;
  }
};
