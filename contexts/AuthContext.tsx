import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, User, AuthError } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { checkNetworkConnectivity } from '@/utils/networkUtils';

interface AuthContextType {
  session: Session | null;
  user: User | null;
  loading: boolean;
  isOnline: boolean;
  signUp: (email: string, password: string, displayName?: string) => Promise<{ error: AuthError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }: any) => {
      console.log('Initial session:', session?.user?.id);
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event: any, session: any) => {
      console.log('Auth state change:', event, session?.user?.id);

      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);

      // Handle different auth events
      if (event === 'SIGNED_IN' && session?.user) {
        console.log('User signed in:', session.user.id);
        try {
          await updateUserProfile(session.user.id, {
            email: session.user.email, // Ensure email is always provided
            last_login: new Date().toISOString(),
          });
        } catch (error) {
          console.error('Failed to update user profile on sign in:', error);
          // Don't block sign in if profile update fails
        }
      } else if (event === 'SIGNED_OUT') {
        console.log('User signed out');
        // `onAuthStateChange` handles session/user updates automatically
      }
    });

    // Monitor network connectivity
    const checkConnectivity = async () => {
      const online = await checkNetworkConnectivity();
      setIsOnline(online);
    };

    // Check connectivity immediately
    checkConnectivity();

    // Set up periodic connectivity checks
    const connectivityInterval = setInterval(checkConnectivity, 30000); // Check every 30 seconds

    return () => {
      subscription.unsubscribe();
      clearInterval(connectivityInterval);
    };
  }, []);

  const createUserProfile = async (userId: string, profileData: any) => {
    try {
      const { error } = await supabase
        .from('users')
        .upsert({
          id: userId,
          ...profileData,
          updated_at: new Date().toISOString(),
        });

      if (error) {
        console.error('Error creating user profile:', error);
      }
    } catch (error) {
      console.error('Error creating user profile:', error);
    }
  };

  const updateUserProfile = async (userId: string, updates: any) => {
    try {
      // First check if user exists, if not create the profile
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('id', userId)
        .single();

      if (!existingUser) {
        // User doesn't exist, create profile first
        await createUserProfile(userId, {
          email: updates.email,
          member_since: new Date().toISOString(),
          created_at: new Date().toISOString(),
        });
      }

      // Now update the user profile
      const { error } = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);

      if (error) {
        console.error('Error updating user profile:', error);
      }
    } catch (error) {
      console.error('Error updating user profile:', error);
    }
  };

  const signUp = async (email: string, password: string, displayName?: string) => {
    const maxRetries = 3;
    let lastError: AuthError | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempting sign up for: ${email} (attempt ${attempt}/${maxRetries})`);

        const { data, error } = await supabase.auth.signUp({
          email,
          password,
        });

        if (error) {
          console.error(`Sign up error (attempt ${attempt}):`, error);
          lastError = error;

          // Don't retry for certain error types
          if (error.message?.includes('User already registered') ||
              error.message?.includes('Password should be at least') ||
              error.message?.includes('Unable to validate email address')) {
            break;
          }

          // Wait before retrying
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt - 1) * 1000;
            console.log(`Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        } else if (data.user) {
          // Create user profile in the users table
          try {
            await createUserProfile(data.user.id, {
              email: data.user.email,
              display_name: displayName || null,
              member_since: new Date().toISOString(),
              created_at: new Date().toISOString(),
            });
          } catch (profileError) {
            console.error('Error creating user profile:', profileError);
            // Don't fail the signup if profile creation fails
          }
          return { error: null };
        }
      } catch (error) {
        console.error(`Sign up exception (attempt ${attempt}):`, error);
        lastError = error as AuthError;

        // For network errors, retry
        if (attempt < maxRetries && (error as Error).message?.includes('Network request failed')) {
          const delay = Math.pow(2, attempt - 1) * 1000;
          console.log(`Network error, retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
      }
    }

    return { error: lastError };
  };

  const signIn = async (email: string, password: string) => {
    const maxRetries = 3;
    let lastError: AuthError | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempting sign in for: ${email} (attempt ${attempt}/${maxRetries})`);

        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          console.error(`Sign in error (attempt ${attempt}):`, error);
          lastError = error;

          // Don't retry for certain error types
          if (error.message?.includes('Invalid login credentials') ||
              error.message?.includes('Email not confirmed') ||
              error.message?.includes('Too many requests')) {
            break;
          }

          // Wait before retrying (exponential backoff)
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
            console.log(`Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        } else {
          console.log('Sign in successful:', data.user?.id);
          return { error: null };
        }
      } catch (error) {
        console.error(`Sign in exception (attempt ${attempt}):`, error);
        lastError = error as AuthError;

        // For network errors, retry
        if (attempt < maxRetries && (error as Error).message?.includes('Network request failed')) {
          const delay = Math.pow(2, attempt - 1) * 1000;
          console.log(`Network error, retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
      }
    }

    return { error: lastError };
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Sign out error:', error);
        setLoading(false); // Set loading to false on error
      }
      // onAuthStateChange will handle success case
      return { error };
    } catch (error) {
      console.error('Sign out exception:', error);
      setLoading(false);
      return { error: error as AuthError };
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      return { error };
    } catch (error) {
      return { error: error as AuthError };
    }
  };

  const value = {
    session,
    user,
    loading,
    isOnline,
    signUp,
    signIn,
    signOut,
    resetPassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
