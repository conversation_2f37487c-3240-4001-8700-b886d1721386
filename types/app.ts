export interface Subject {
  id: string;
  name: string;
  color: string;
  createdAt: Date;
}

export interface TimerSession {
  id: string;
  userId?: string; // Add userId field
  startTime: Date;
  endTime?: Date | null;
  duration: number; // in seconds
  subject?: string;
  subjectColor?: string;
  mode: 'stopwatch' | 'pomodoro';
  phase?: 'work' | 'shortBreak' | 'longBreak';
  completed: boolean;
  notes?: string;
  taskName?: string;
  taskType?: string;
  productivityRating?: number;
  feedback?: string;
  date: string; // YYYY-MM-DD format
  // Legacy fields for backward compatibility
  subjectId?: string;
  isBreak?: boolean;
}

// Enhanced Analytics Types
export interface DailyStat {
  date: string; // YYYY-MM-DD
  totalDuration: number; // seconds
  targetAchieved: boolean;
  sessionCount: number;
  completedPomodoros: number;
  subjectDurations: { [subject: string]: number };
  taskTypeDurations: { [taskType: string]: number };
  averageProductivityRating: number;
  streakDay: boolean;
}

export interface WeeklyStat {
  weekStart: string; // YYYY-MM-DD
  weekEnd: string; // YYYY-MM-DD
  totalDuration: number;
  sessionCount: number;
  completedPomodoros: number;
  dailyAverage: number;
  subjectDurations: { [subject: string]: number };
  taskTypeDurations: { [taskType: string]: number };
  targetAchievementRate: number;
  targetAchieved: boolean;
  activeDays: number;
  averageProductivityRating: number;
}

export interface MonthlyStat {
  month: string; // YYYY-MM
  year: number;
  totalDuration: number;
  sessionCount: number;
  completedPomodoros: number;
  dailyAverage: number;
  weeklyAverage: number;
  subjectDurations: { [subject: string]: number };
  taskTypeDurations: { [taskType: string]: number };
  targetAchievementRate: number;
  streakDays: number;
  targetAchieved: boolean;
  activeDays: number;
  averageProductivityRating: number;
}

export interface SubjectStat {
  subject: string;
  color: string;
  totalDuration: number;
  sessionCount: number;
  averageSessionDuration: number;
  averageProductivityRating: number;
  lastStudied: string; // YYYY-MM-DD
  streakDays: number;
  percentage: number;
}

export interface TaskTypeStat {
  taskType: string;
  totalDuration: number;
  sessionCount: number;
  averageSessionDuration: number;
  averageProductivityRating: number;
  percentage: number;
}

export interface Analytics {
  dailyStats: DailyStat[];
  weeklyStats: WeeklyStat[];
  monthlyStats: MonthlyStat[];
  subjectStats: SubjectStat[];
  taskTypeStats: TaskTypeStat[];
  streakInfo: StreakInfo;
  dateRange: DateRange;
  generatedAt: Date;
}

export interface StreakInfo {
  currentStreak: number;
  longestStreak: number;
  streakStartDate: string;
  isActiveToday: boolean;
}

export interface UserProfile {
  id: string;
  email: string;
  username?: string;
  display_name?: string;
  photo_url?: string;
  daily_target?: number; // in minutes
  daily_motivation?: string;
  day_start_time?: number; // hour 0-23
  created_at: Date;
  updated_at: Date;
}

export interface Goal {
  id: string;
  title: string;
  description: string;
  targetDate: Date;
  subjectId?: string;
  priority: 'low' | 'medium' | 'high';
  completed: boolean;
  createdAt: Date;
}

// Enhanced Task Management Types
export interface Task {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'todo' | 'in_progress' | 'completed' | 'cancelled';
  due_date?: Date;
  start_date?: Date;
  completion_date?: Date;
  estimated_duration?: number; // in minutes
  actual_duration?: number; // in minutes
  progress_percentage: number;
  subject_id?: string;
  tags: string[];
  is_milestone: boolean;
  parent_task_id?: string;
  order_index: number;
  reminder_enabled: boolean;
  reminder_time?: Date;
  study_session_ids: string[];
  total_study_time: number; // in minutes
  notes?: string;
  attachments: any[];
  metadata: any;
  created_at: Date;
  updated_at: Date;
}

export interface TaskCategory {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  order_index: number;
  is_default: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface TaskMilestone {
  id: string;
  task_id: string;
  title: string;
  description?: string;
  target_percentage: number;
  achieved_at?: Date;
  is_achieved: boolean;
  reward_points: number;
  created_at: Date;
}

export interface TaskFilters {
  status?: string[];
  category?: string[];
  priority?: string[];
  due_date_from?: Date;
  due_date_to?: Date;
  search?: string;
  tags?: string[];
  is_milestone?: boolean;
  parent_task_id?: string;
}

export interface TaskStats {
  total_tasks: number;
  completed_tasks: number;
  in_progress_tasks: number;
  overdue_tasks: number;
  completion_rate: number;
  total_study_time: number;
  average_completion_time: number;
  tasks_by_category: { [key: string]: number };
  tasks_by_priority: { [key: string]: number };
}

export interface PomodoroSettings {
  workDuration: number; // in minutes
  shortBreakDuration: number; // in minutes
  longBreakDuration: number; // in minutes
  sessionsUntilLongBreak: number;
}

// Timer State Management Types
export type TimerStatus = "idle" | "running" | "paused";
export type TimerMode = "pomodoro" | "stopwatch";
export type PhaseType = "work" | "shortBreak" | "longBreak";

export interface TimerState {
  status: TimerStatus;
  mode: TimerMode;
  currentPhase: PhaseType;
  startTime: number | null;
  accumulatedPausedTime: number;
  pauseStartTime: number | null;
  completedSessions: number;
  selectedSubject: string;
  taskName: string;
  taskType: string;
  elapsedTime: number;
  timeRemaining: number;
}

export interface SessionSummary {
  duration?: number;
  subject?: string;
  taskName?: string;
  taskType?: string;
  mode?: TimerMode;
  phase?: PhaseType;
  completed?: boolean;
  productivityRating?: number;
  feedback?: string;
  notes?: string;
}

// Notification Types
export interface NotificationSettings {
  enabled: boolean;
  intervals: number[]; // minutes
  sound: boolean;
  vibration: boolean;
  sessionComplete: boolean;
  breakReminder: boolean;
}

// Data Sync Types
export interface SyncStatus {
  lastSync: Date;
  pendingChanges: number;
  isOnline: boolean;
  syncInProgress: boolean;
}

// Error Handling Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: AppError;
  success: boolean;
}

import { RealtimeChannel } from '@supabase/supabase-js';

// Real-time Subscription Types
export type RealtimeSubscription = RealtimeChannel;

// Task Types for Timer
export const TASK_TYPES = [
  "Lecture",
  "Exercise",
  "Reading",
  "Practice",
  "Review",
  "General Study",
  "Custom"
] as const;

export type TaskType = typeof TASK_TYPES[number];

// Period Types for Analytics
export type AnalyticsPeriod = "today" | "week" | "month" | "year" | "all" | "custom";

export interface DateRange {
  start: Date;
  end: Date;
}

// Chart Data Types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  percentage?: number;
}

export interface TimeSeriesDataPoint {
  date: string;
  value: number;
  label?: string;
}
