import { useState, useEffect } from 'react';
import { Subject } from '@/types/app';
import { Platform } from 'react-native';
import { storageService } from '@/services/storageService';
import { supabaseService } from '@/services/supabaseService';
import { useAuth } from '@/contexts/AuthContext';
import { useSubjectStore } from '@/stores/subjectStore';

const STORAGE_KEY = 'isotope_subjects';

export function useSubjects() {
  const { user } = useAuth();

  // Use the enhanced subject store for better functionality
  const store = useSubjectStore();

  // Legacy state for backward compatibility
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user) {
      // Use the enhanced store to fetch subjects
      store.fetchSubjects(user.id);
    }
  }, [user]);

  // Sync local state with store state for backward compatibility
  useEffect(() => {
    setSubjects(store.subjects);
    setLoading(store.isLoading);
  }, [store.subjects, store.isLoading]);

  const loadSubjects = async () => {
    if (!user) return;

    try {
      setLoading(true);
      // Try to load from Supabase first
      const supabaseSubjects = await supabaseService.getUserSubjects(user.id);
      if (supabaseSubjects.length > 0) {
        setSubjects(supabaseSubjects);
      } else {
        // Fallback to local storage for migration
        const stored = await storageService.getObject<any[]>(STORAGE_KEY);
        if (stored) {
          const localSubjects = stored.map((s: any) => ({
            ...s,
            createdAt: new Date(s.createdAt),
          }));
          setSubjects(localSubjects);

          // Migrate local subjects to Supabase
          for (const subject of localSubjects) {
            await supabaseService.createUserSubject(user.id, subject);
          }
        }
      }
    } catch (error) {
      console.error('Error loading subjects:', error);
      // Fallback to local storage on error
      try {
        const stored = await storageService.getObject<any[]>(STORAGE_KEY);
        if (stored) {
          setSubjects(stored.map((s: any) => ({
            ...s,
            createdAt: new Date(s.createdAt),
          })));
        }
      } catch (localError) {
        console.error('Error loading local subjects:', localError);
      }
    } finally {
      setLoading(false);
    }
  };

  const saveSubjects = async (newSubjects: Subject[]) => {
    try {
      // Save to local storage for offline support
      await storageService.setObject(STORAGE_KEY, newSubjects);
      setSubjects(newSubjects);
    } catch (error) {
      console.error('Error saving subjects:', error);
    }
  };

  const addSubject = async (subjectData: Omit<Subject, 'id' | 'createdAt'>) => {
    if (!user) return;

    try {
      return await store.addSubject(user.id, subjectData.name, subjectData.color);
    } catch (error) {
      console.error('Error adding subject:', error);
      throw error;
    }
  };

  const updateSubject = async (id: string, updates: Partial<Subject>) => {
    try {
      return await store.updateSubject(id, updates);
    } catch (error) {
      console.error('Error updating subject:', error);
      throw error;
    }
  };

  const deleteSubject = async (id: string) => {
    try {
      await store.deleteSubject(id);
    } catch (error) {
      console.error('Error deleting subject:', error);
      throw error;
    }
  };

  const getSubjectById = (id: string) => {
    return subjects.find(subject => subject.id === id);
  };

  return {
    subjects,
    loading,
    addSubject,
    updateSubject,
    deleteSubject,
    getSubjectById,
  };
}