import { useMemo } from 'react';
import { StyleSheet } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Theme } from '@/types/theme';

/**
 * Hook for creating themed styles that automatically update when theme changes
 * @param createStyles Function that takes theme and returns StyleSheet
 * @returns Memoized styles object
 */
export function useThemedStyles<T>(
  createStyles: (theme: Theme) => T
): T {
  const { theme } = useTheme();
  
  return useMemo(() => createStyles(theme), [theme, createStyles]);
}

/**
 * Utility function to create a style factory for components
 * @param styleFactory Function that creates styles based on theme
 * @returns Function that can be used with useThemedStyles
 */
export function createThemedStyles<T>(
  styleFactory: (theme: Theme) => T
) {
  return styleFactory;
}

/**
 * Helper to get color with opacity
 * @param color Base color
 * @param opacity Opacity value (0-1)
 * @returns Color with opacity
 */
export function withOpacity(color: string, opacity: number): string {
  // Handle hex colors
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
    return `${color}${alpha}`;
  }
  
  // Handle rgba colors
  if (color.startsWith('rgba')) {
    return color.replace(/[\d\.]+\)$/g, `${opacity})`);
  }
  
  // Handle rgb colors
  if (color.startsWith('rgb')) {
    return color.replace('rgb', 'rgba').replace(')', `, ${opacity})`);
  }
  
  return color;
}

/**
 * Helper to create gradient colors array
 * @param colors Array of colors
 * @param opacity Optional opacity to apply to all colors
 * @returns Array of colors with optional opacity
 */
export function createGradient(colors: string[], opacity?: number): string[] {
  if (opacity !== undefined) {
    return colors.map(color => withOpacity(color, opacity));
  }
  return colors;
}
