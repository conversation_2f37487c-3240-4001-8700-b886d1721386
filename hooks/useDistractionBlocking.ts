import { useState, useEffect, useCallback } from 'react';
import { Platform, Alert } from 'react-native';
import { mobileAppMonitoringService, FocusContext, DistractionAttempt } from '@/services/mobileAppMonitoringService';
import { unifiedDistractionBlockingService, UnifiedBlockedApp, BlockingCapabilities } from '@/services/unifiedDistractionBlockingService';
import { storageService } from '@/services/storageService';
import { notificationService } from '@/services/notificationService';

export interface BlockedApp {
  id: string;
  name: string;
  packageName: string;
  icon: string;
  category: string;
  isBlocked: boolean;
  blockDuringFocus: boolean;
  blockDuringBreaks: boolean;
}

export interface IOSBlockingState {
  isAuthorized: boolean;
  capabilities: BlockingCapabilities | null;
  canUseNativeUI: boolean;
}

export interface BlockedWebsite {
  id: string;
  name: string;
  url: string;
  domain: string;
  category: string;
  isBlocked: boolean;
  blockDuringFocus: boolean;
  blockDuringBreaks: boolean;
}

export interface BlockingSettings {
  isEnabled: boolean;
  strictMode: boolean;
  blockDuringBreaks: boolean;
  showNotifications: boolean;
  hapticFeedback: boolean;
  autoStartWithTimer: boolean;
  allowManualActivation: boolean;
}

export interface BlockingStats {
  blockedToday: number;
  blockedThisWeek: number;
  streakDays: number;
  blockingEffectiveness: number;
  totalFocusTime: number;
}

export interface RecentlyBlockedApp {
  name: string;
  attempts: number;
  lastAttempt: Date;
}

const STORAGE_KEY = 'distraction_blocking_data';

export function useDistractionBlocking() {
  const [blockedApps, setBlockedApps] = useState<BlockedApp[]>([]);
  const [blockedWebsites, setBlockedWebsites] = useState<BlockedWebsite[]>([]);
  const [blockingSettings, setBlockingSettings] = useState<BlockingSettings>({
    isEnabled: false,
    strictMode: false,
    blockDuringBreaks: false,
    showNotifications: true,
    hapticFeedback: true,
    autoStartWithTimer: true,
    allowManualActivation: true,
  });
  const [stats, setStats] = useState<BlockingStats>({
    blockedToday: 0,
    blockedThisWeek: 0,
    streakDays: 0,
    blockingEffectiveness: 85,
    totalFocusTime: 0,
  });
  const [recentlyBlockedApps, setRecentlyBlockedApps] = useState<RecentlyBlockedApp[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isActivelyBlocking, setIsActivelyBlocking] = useState(false);
  const [currentBlockingReason, setCurrentBlockingReason] = useState<'timer' | 'manual' | null>(null);

  // iOS-specific state
  const [iosBlockingState, setIOSBlockingState] = useState<IOSBlockingState>({
    isAuthorized: false,
    capabilities: null,
    canUseNativeUI: false
  });

  // Initialize iOS capabilities
  useEffect(() => {
    initializeIOSCapabilities();
  }, []);

  // Load data on mount
  useEffect(() => {
    loadData();
    setupMobileMonitoring();
    
    return () => {
      mobileAppMonitoringService.destroy();
    };
  }, []);

  // Update mobile monitoring when settings change
  useEffect(() => {
    if (blockingSettings.isEnabled) {
      const blockedPackages = blockedApps
        .filter(app => app.isBlocked)
        .map(app => app.packageName);
      
      mobileAppMonitoringService.updateBlockedApps(blockedPackages);
      mobileAppMonitoringService.setStrictMode(blockingSettings.strictMode);
    }
  }, [blockedApps, blockingSettings]);

  const setupMobileMonitoring = () => {
    // Listen for distraction attempts
    const unsubscribeDistraction = mobileAppMonitoringService.onDistractionAttempt(
      (attempt: DistractionAttempt) => {
        recordDistractionAttempt(attempt.appName, attempt.packageName);
        updateStats(attempt);
      }
    );

    return unsubscribeDistraction;
  };

  const initializeIOSCapabilities = async () => {
    try {
      const capabilities = await unifiedDistractionBlockingService.getCapabilities();
      const isAuthorized = await unifiedDistractionBlockingService.isAuthorized();

      setIOSBlockingState({
        isAuthorized,
        capabilities,
        canUseNativeUI: capabilities.hasNativeUI && isAuthorized
      });

      console.log('📱 iOS capabilities initialized:', { capabilities, isAuthorized });
    } catch (error) {
      console.error('📱 Failed to initialize iOS capabilities:', error);
    }
  };

  const loadData = async () => {
    try {
      setIsLoading(true);

      const data = await storageService.getObject<any>(STORAGE_KEY);
      if (data) {
        setBlockedApps(data.blockedApps || []);
        setBlockedWebsites(data.blockedWebsites || []);
        setBlockingSettings(data.blockingSettings || blockingSettings);
        setStats(data.stats || stats);
        setRecentlyBlockedApps(data.recentlyBlockedApps || []);
      }

      // Add some default blocked apps if none exist
      if (blockedApps.length === 0) {
        await addDefaultBlockedApps();
      }
    } catch (error) {
      console.error('Error loading distraction blocking data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (data: any) => {
    try {
      await storageService.setObject(STORAGE_KEY, data);
    } catch (error) {
      console.error('Error saving distraction blocking data:', error);
      throw error;
    }
  };

  const addDefaultBlockedApps = async () => {
    const defaultApps: Omit<BlockedApp, 'id'>[] = [
      {
        name: 'Instagram',
        packageName: 'com.instagram.android',
        icon: '📷',
        category: 'social',
        isBlocked: true,
        blockDuringFocus: true,
        blockDuringBreaks: false,
      },
      {
        name: 'TikTok',
        packageName: 'com.zhiliaoapp.musically',
        icon: '🎵',
        category: 'social',
        isBlocked: true,
        blockDuringFocus: true,
        blockDuringBreaks: false,
      },
      {
        name: 'YouTube',
        packageName: 'com.google.android.youtube',
        icon: '📺',
        category: 'entertainment',
        isBlocked: true,
        blockDuringFocus: true,
        blockDuringBreaks: false,
      },
    ];

    for (const app of defaultApps) {
      await addBlockedApp(app);
    }
  };

  const updateBlockingSettings = useCallback(async (updates: Partial<BlockingSettings>) => {
    const newSettings = { ...blockingSettings, ...updates };
    setBlockingSettings(newSettings);
    
    const data = {
      blockedApps,
      blockedWebsites,
      blockingSettings: newSettings,
      stats,
      recentlyBlockedApps,
    };
    await saveData(data);
  }, [blockedApps, blockedWebsites, blockingSettings, stats, recentlyBlockedApps]);

  const addBlockedApp = useCallback(async (app: Omit<BlockedApp, 'id'>) => {
    const newApp: BlockedApp = {
      ...app,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    };
    
    const newApps = [...blockedApps, newApp];
    setBlockedApps(newApps);
    
    const data = {
      blockedApps: newApps,
      blockedWebsites,
      blockingSettings,
      stats,
      recentlyBlockedApps,
    };
    await saveData(data);
  }, [blockedApps, blockedWebsites, blockingSettings, stats, recentlyBlockedApps]);

  const updateBlockedApp = useCallback(async (appId: string, updates: Partial<BlockedApp>) => {
    const newApps = blockedApps.map(app =>
      app.id === appId ? { ...app, ...updates } : app
    );
    setBlockedApps(newApps);
    
    const data = {
      blockedApps: newApps,
      blockedWebsites,
      blockingSettings,
      stats,
      recentlyBlockedApps,
    };
    await saveData(data);
  }, [blockedApps, blockedWebsites, blockingSettings, stats, recentlyBlockedApps]);

  const removeBlockedApp = useCallback(async (appId: string) => {
    const newApps = blockedApps.filter(app => app.id !== appId);
    setBlockedApps(newApps);
    
    const data = {
      blockedApps: newApps,
      blockedWebsites,
      blockingSettings,
      stats,
      recentlyBlockedApps,
    };
    await saveData(data);
  }, [blockedApps, blockedWebsites, blockingSettings, stats, recentlyBlockedApps]);

  const addBlockedWebsite = useCallback(async (website: Omit<BlockedWebsite, 'id'>) => {
    const newWebsite: BlockedWebsite = {
      ...website,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    };
    
    const newWebsites = [...blockedWebsites, newWebsite];
    setBlockedWebsites(newWebsites);
    
    const data = {
      blockedApps,
      blockedWebsites: newWebsites,
      blockingSettings,
      stats,
      recentlyBlockedApps,
    };
    await saveData(data);
  }, [blockedApps, blockedWebsites, blockingSettings, stats, recentlyBlockedApps]);

  const updateBlockedWebsite = useCallback(async (websiteId: string, updates: Partial<BlockedWebsite>) => {
    const newWebsites = blockedWebsites.map(website =>
      website.id === websiteId ? { ...website, ...updates } : website
    );
    setBlockedWebsites(newWebsites);
    
    const data = {
      blockedApps,
      blockedWebsites: newWebsites,
      blockingSettings,
      stats,
      recentlyBlockedApps,
    };
    await saveData(data);
  }, [blockedApps, blockedWebsites, blockingSettings, stats, recentlyBlockedApps]);

  const removeBlockedWebsite = useCallback(async (websiteId: string) => {
    const newWebsites = blockedWebsites.filter(website => website.id !== websiteId);
    setBlockedWebsites(newWebsites);
    
    const data = {
      blockedApps,
      blockedWebsites: newWebsites,
      blockingSettings,
      stats,
      recentlyBlockedApps,
    };
    await saveData(data);
  }, [blockedApps, blockedWebsites, blockingSettings, stats, recentlyBlockedApps]);

  const setFocusContext = useCallback((context: FocusContext) => {
    if (blockingSettings.isEnabled || isActivelyBlocking) {
      unifiedDistractionBlockingService.setContext(context);
      mobileAppMonitoringService.setContext(context);
    }
  }, [blockingSettings.isEnabled, isActivelyBlocking]);

  // Auto-start blocking when timer starts
  const startTimerBlocking = useCallback(async () => {
    if (blockingSettings.autoStartWithTimer) {
      setIsActivelyBlocking(true);
      setCurrentBlockingReason('timer');

      // Convert blocked apps to unified format
      const unifiedApps: UnifiedBlockedApp[] = blockedApps
        .filter(app => app.isBlocked)
        .map(app => ({
          id: app.id,
          name: app.name,
          packageName: app.packageName,
          icon: app.icon,
          category: app.category,
          isBlocked: app.isBlocked,
          blockDuringFocus: app.blockDuringFocus,
          blockDuringBreaks: app.blockDuringBreaks
        }));

      // Start unified blocking
      const success = await unifiedDistractionBlockingService.startBlocking(unifiedApps);

      if (success) {
        unifiedDistractionBlockingService.setContext('focus');

        // Show notification
        if (unifiedApps.length > 0) {
          await notificationService.showTimerBlockingStarted();
        }
      } else {
        console.warn('📱 Failed to start unified blocking, falling back to monitoring service');

        // Fallback to monitoring service
        const blockedPackages = blockedApps
          .filter(app => app.isBlocked)
          .map(app => app.packageName);
        mobileAppMonitoringService.updateBlockedApps(blockedPackages);
        mobileAppMonitoringService.setStrictMode(blockingSettings.strictMode);
        mobileAppMonitoringService.setBlockingActive(true);
        mobileAppMonitoringService.setContext('focus');
      }
    }
  }, [blockingSettings.autoStartWithTimer, blockingSettings.strictMode, blockedApps]);

  // Stop timer-based blocking
  const stopTimerBlocking = useCallback(async () => {
    if (currentBlockingReason === 'timer') {
      setIsActivelyBlocking(false);
      setCurrentBlockingReason(null);

      // Stop unified blocking
      await unifiedDistractionBlockingService.stopBlocking();
      unifiedDistractionBlockingService.setContext('normal');

      // Also stop monitoring service (fallback)
      mobileAppMonitoringService.setBlockingActive(false);
      mobileAppMonitoringService.setContext('normal');

      await notificationService.showBlockingDeactivated();
    }
  }, [currentBlockingReason]);

  // Manual blocking control
  const startManualBlocking = useCallback(async () => {
    if (blockingSettings.allowManualActivation) {
      setIsActivelyBlocking(true);
      setCurrentBlockingReason('manual');

      // Update blocked apps in monitoring service
      const blockedPackages = blockedApps
        .filter(app => app.isBlocked)
        .map(app => app.packageName);
      mobileAppMonitoringService.updateBlockedApps(blockedPackages);
      mobileAppMonitoringService.setStrictMode(blockingSettings.strictMode);
      mobileAppMonitoringService.setBlockingActive(true);
      mobileAppMonitoringService.setContext('focus');

      // Show notification
      if (blockedPackages.length > 0) {
        await notificationService.showManualBlockingStarted();
      }
    }
  }, [blockingSettings.allowManualActivation, blockingSettings.strictMode, blockedApps]);

  const stopManualBlocking = useCallback(async () => {
    if (currentBlockingReason === 'manual') {
      setIsActivelyBlocking(false);
      setCurrentBlockingReason(null);
      mobileAppMonitoringService.setBlockingActive(false);
      mobileAppMonitoringService.setContext('normal');
      await notificationService.showBlockingDeactivated();
    }
  }, [currentBlockingReason]);

  // Toggle manual blocking
  const toggleManualBlocking = useCallback(() => {
    if (isActivelyBlocking && currentBlockingReason === 'manual') {
      stopManualBlocking();
    } else if (!isActivelyBlocking) {
      startManualBlocking();
    }
  }, [isActivelyBlocking, currentBlockingReason, startManualBlocking, stopManualBlocking]);

  const recordDistractionAttempt = useCallback((appName: string, packageName: string) => {
    setRecentlyBlockedApps(prev => {
      const existing = prev.find(app => app.name === appName);
      if (existing) {
        return prev.map(app =>
          app.name === appName
            ? { ...app, attempts: app.attempts + 1, lastAttempt: new Date() }
            : app
        );
      } else {
        return [...prev, { name: appName, attempts: 1, lastAttempt: new Date() }].slice(0, 5);
      }
    });
  }, []);

  const updateStats = useCallback((attempt: DistractionAttempt) => {
    setStats(prev => ({
      ...prev,
      blockedToday: prev.blockedToday + (attempt.blocked ? 1 : 0),
      blockedThisWeek: prev.blockedThisWeek + (attempt.blocked ? 1 : 0),
    }));
  }, []);

  // Simulation function for testing
  const simulateAppBlock = useCallback((appName: string, packageName: string) => {
    mobileAppMonitoringService.simulateDistractionAttempt(appName, packageName);
  }, []);

  // iOS-specific functions
  const requestIOSPermissions = useCallback(async (): Promise<boolean> => {
    try {
      const granted = await unifiedDistractionBlockingService.requestPermissions();
      if (granted) {
        await initializeIOSCapabilities();
      }
      return granted;
    } catch (error) {
      console.error('📱 Failed to request iOS permissions:', error);
      Alert.alert(
        'Permission Required',
        'Screen Time permission is required to block apps during focus sessions. Please grant permission in Settings.',
        [{ text: 'OK' }]
      );
      return false;
    }
  }, []);

  const presentIOSAppSelector = useCallback(async (): Promise<UnifiedBlockedApp[]> => {
    try {
      if (!iosBlockingState.isAuthorized) {
        const granted = await requestIOSPermissions();
        if (!granted) {
          throw new Error('iOS permissions not granted');
        }
      }

      const selectedApps = await unifiedDistractionBlockingService.presentAppSelectionUI();

      // Add selected apps to our blocked apps list
      for (const app of selectedApps) {
        await addBlockedApp({
          name: app.name,
          packageName: app.packageName,
          icon: app.icon,
          category: app.category,
          isBlocked: true,
          blockDuringFocus: true,
          blockDuringBreaks: false,
        });
      }

      return selectedApps;
    } catch (error) {
      console.error('📱 Failed to present iOS app selector:', error);
      Alert.alert(
        'App Selection Failed',
        'Unable to show app selection. Please ensure Screen Time permissions are granted.',
        [{ text: 'OK' }]
      );
      return [];
    }
  }, [iosBlockingState.isAuthorized, requestIOSPermissions, addBlockedApp]);

  const getIOSCapabilities = useCallback((): BlockingCapabilities | null => {
    return iosBlockingState.capabilities;
  }, [iosBlockingState.capabilities]);

  return {
    // Data
    blockedApps,
    blockedWebsites,
    blockingSettings,
    stats,
    recentlyBlockedApps,
    isLoading,

    // Computed values
    isBlockingEnabled: blockingSettings.isEnabled,
    isActivelyBlocking,
    currentBlockingReason,

    // iOS state
    iosBlockingState,

    // Actions
    updateBlockingSettings,
    addBlockedApp,
    updateBlockedApp,
    removeBlockedApp,
    addBlockedWebsite,
    updateBlockedWebsite,
    removeBlockedWebsite,
    setFocusContext,
    recordDistractionAttempt,
    simulateAppBlock,

    // Timer integration
    startTimerBlocking,
    stopTimerBlocking,

    // Manual control
    startManualBlocking,
    stopManualBlocking,
    toggleManualBlocking,

    // iOS-specific functions
    requestIOSPermissions,
    presentIOSAppSelector,
    getIOSCapabilities,
  };
}
