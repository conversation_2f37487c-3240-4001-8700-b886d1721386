import { useState, useEffect, useRef, useCallback } from 'react';
import { AppState, Platform, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  TimerSession, 
  Subject, 
  PomodoroSettings, 
  TimerState, 
  TimerStatus, 
  TimerMode, 
  PhaseType,
  SessionSummary,
  TaskType,
  TASK_TYPES
} from '@/types/app';
import { enhancedSupabaseService } from '@/services/enhancedSupabaseService';
import { useAuth } from '@/contexts/AuthContext';
import { useSubjects } from './useSubjects';

const TIMER_STATE_KEY = 'isotope_enhanced_timer_state';
const POMODORO_SETTINGS_KEY = 'isotope_pomodoro_settings';

const DEFAULT_POMODORO_SETTINGS: PomodoroSettings = {
  workDuration: 25,
  shortBreakDuration: 5,
  longBreakDuration: 15,
  sessionsUntilLongBreak: 4,
};

export function useEnhancedTimer() {
  const { user } = useAuth();
  const { subjects } = useSubjects();
  
  // Timer state
  const [timerState, setTimerState] = useState<TimerState>({
    status: 'idle',
    mode: 'stopwatch',
    currentPhase: 'work',
    startTime: null,
    accumulatedPausedTime: 0,
    pauseStartTime: null,
    completedSessions: 0,
    selectedSubject: '',
    taskName: '',
    taskType: 'General Study',
    elapsedTime: 0,
    timeRemaining: 0
  });

  const [pomodoroSettings, setPomodoroSettings] = useState<PomodoroSettings>(DEFAULT_POMODORO_SETTINGS);
  const [sessions, setSessions] = useState<TimerSession[]>([]);
  const [loading, setLoading] = useState(false);

  // Refs for background handling
  const intervalRef = useRef<number | null>(null);
  const backgroundTimeRef = useRef<number>(0);
  const appStateRef = useRef(AppState.currentState);

  // Session persistence
  const saveTimerState = useCallback(async (state: TimerState) => {
    try {
      const stateToSave = {
        ...state,
        saveTimestamp: Date.now()
      };
      await AsyncStorage.setItem(TIMER_STATE_KEY, JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Error saving timer state:', error);
    }
  }, []);

  const loadTimerState = useCallback(async () => {
    try {
      const savedState = await AsyncStorage.getItem(TIMER_STATE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        const timeDiff = Date.now() - (parsedState.saveTimestamp || 0);
        
        // If timer was running when app was backgrounded, calculate elapsed time
        if (parsedState.status === 'running' && parsedState.startTime) {
          const backgroundElapsed = Math.floor(timeDiff / 1000);
          parsedState.elapsedTime += backgroundElapsed;
          
          if (parsedState.mode === 'pomodoro') {
            parsedState.timeRemaining = Math.max(0, parsedState.timeRemaining - backgroundElapsed);
          }
        }
        
        setTimerState(parsedState);
        return parsedState;
      }
    } catch (error) {
      console.error('Error loading timer state:', error);
    }
    return null;
  }, []);

  // Initialize timer
  useEffect(() => {
    loadTimerState();
    loadPomodoroSettings();
    
    // Handle app state changes for background timer
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
        // App came to foreground
        loadTimerState();
      } else if (nextAppState.match(/inactive|background/)) {
        // App went to background
        saveTimerState(timerState);
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  // Timer tick effect
  useEffect(() => {
    if (timerState.status === 'running') {
      intervalRef.current = setInterval(() => {
        setTimerState(prevState => {
          const newState = { ...prevState };
          
          if (prevState.mode === 'stopwatch') {
            newState.elapsedTime = prevState.elapsedTime + 1;
          } else if (prevState.mode === 'pomodoro') {
            newState.timeRemaining = Math.max(0, prevState.timeRemaining - 1);
            newState.elapsedTime = prevState.elapsedTime + 1;
            
            // Check if pomodoro completed
            if (newState.timeRemaining === 0) {
              handlePomodoroComplete(newState);
              return newState;
            }
          }
          
          return newState;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [timerState.status]);

  // Auto-save timer state
  useEffect(() => {
    if (timerState.status !== 'idle') {
      saveTimerState(timerState);
    }
  }, [timerState, saveTimerState]);

  const loadPomodoroSettings = async () => {
    try {
      const stored = await AsyncStorage.getItem(POMODORO_SETTINGS_KEY);
      if (stored) {
        setPomodoroSettings(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading pomodoro settings:', error);
    }
  };

  const savePomodoroSettings = async (settings: PomodoroSettings) => {
    try {
      await AsyncStorage.setItem(POMODORO_SETTINGS_KEY, JSON.stringify(settings));
      setPomodoroSettings(settings);
    } catch (error) {
      console.error('Error saving pomodoro settings:', error);
    }
  };

  const startTimer = useCallback(() => {
    const now = Date.now();
    
    setTimerState(prevState => {
      const newState = { ...prevState };
      
      if (prevState.status === 'idle') {
        // Starting fresh
        newState.startTime = now;
        newState.accumulatedPausedTime = 0;
        newState.elapsedTime = 0;
        
        if (prevState.mode === 'pomodoro') {
          newState.timeRemaining = getPhaseTime(prevState.currentPhase);
        }
      } else if (prevState.status === 'paused') {
        // Resuming from pause
        if (prevState.pauseStartTime) {
          newState.accumulatedPausedTime += now - prevState.pauseStartTime;
        }
        newState.pauseStartTime = null;
      }
      
      newState.status = 'running';
      return newState;
    });
  }, []);

  const pauseTimer = useCallback(() => {
    setTimerState(prevState => ({
      ...prevState,
      status: 'paused',
      pauseStartTime: Date.now()
    }));
  }, []);

  const resetTimer = useCallback(() => {
    setTimerState(prevState => ({
      ...prevState,
      status: 'idle',
      startTime: null,
      accumulatedPausedTime: 0,
      pauseStartTime: null,
      elapsedTime: 0,
      timeRemaining: prevState.mode === 'pomodoro' ? getPhaseTime('work') : 0,
      currentPhase: 'work',
      completedSessions: 0
    }));
  }, []);

  const completeSession = useCallback(async (summary?: SessionSummary) => {
    if (!user || timerState.status === 'idle') return;

    try {
      setLoading(true);
      
      const session: Omit<TimerSession, 'id'> = {
        startTime: new Date(timerState.startTime!),
        endTime: new Date(),
        duration: timerState.elapsedTime,
        subject: timerState.selectedSubject || undefined,
        subjectColor: subjects.find(s => s.name === timerState.selectedSubject)?.color,
        mode: timerState.mode,
        phase: timerState.currentPhase,
        completed: true,
        notes: summary?.notes,
        taskName: timerState.taskName || summary?.taskName,
        taskType: timerState.taskType || summary?.taskType,
        productivityRating: summary?.productivityRating,
        feedback: summary?.feedback,
        date: new Date().toISOString().split('T')[0]
      };

      const response = await enhancedSupabaseService.createStudySession(user.id, session);
      
      if (response.success && response.data) {
        setSessions(prev => [response.data!, ...prev]);
      }

      resetTimer();
    } catch (error) {
      console.error('Error completing session:', error);
    } finally {
      setLoading(false);
    }
  }, [user, timerState, subjects, resetTimer]);

  const switchMode = useCallback((mode: TimerMode) => {
    resetTimer();
    setTimerState(prevState => ({
      ...prevState,
      mode,
      timeRemaining: mode === 'pomodoro' ? getPhaseTime('work') : 0
    }));
  }, [resetTimer]);

  const setSelectedSubject = useCallback((subject: string) => {
    setTimerState(prevState => ({
      ...prevState,
      selectedSubject: subject
    }));
  }, []);

  const setTaskName = useCallback((taskName: string) => {
    setTimerState(prevState => ({
      ...prevState,
      taskName
    }));
  }, []);

  const setTaskType = useCallback((taskType: TaskType) => {
    setTimerState(prevState => ({
      ...prevState,
      taskType
    }));
  }, []);

  const getPhaseTime = (phase: PhaseType): number => {
    switch (phase) {
      case 'work':
        return pomodoroSettings.workDuration * 60;
      case 'shortBreak':
        return pomodoroSettings.shortBreakDuration * 60;
      case 'longBreak':
        return pomodoroSettings.longBreakDuration * 60;
      default:
        return pomodoroSettings.workDuration * 60;
    }
  };

  const handlePomodoroComplete = (state: TimerState) => {
    // Auto-complete current session
    completeSession();
    
    // Determine next phase
    let nextPhase: PhaseType;
    let nextCompletedSessions = state.completedSessions;
    
    if (state.currentPhase === 'work') {
      nextCompletedSessions += 1;
      nextPhase = nextCompletedSessions % pomodoroSettings.sessionsUntilLongBreak === 0 
        ? 'longBreak' 
        : 'shortBreak';
    } else {
      nextPhase = 'work';
    }
    
    setTimerState(prevState => ({
      ...prevState,
      currentPhase: nextPhase,
      timeRemaining: getPhaseTime(nextPhase),
      completedSessions: nextCompletedSessions,
      status: 'idle',
      startTime: null,
      elapsedTime: 0
    }));
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return {
    // State
    timerState,
    pomodoroSettings,
    sessions,
    loading,
    
    // Actions
    startTimer,
    pauseTimer,
    resetTimer,
    completeSession,
    switchMode,
    setSelectedSubject,
    setTaskName,
    setTaskType,
    savePomodoroSettings,
    
    // Utilities
    formatTime,
    getPhaseTime,
    
    // Computed values
    isRunning: timerState.status === 'running',
    isPaused: timerState.status === 'paused',
    isIdle: timerState.status === 'idle',
    displayTime: timerState.mode === 'stopwatch' ? timerState.elapsedTime : timerState.timeRemaining,
    availableTaskTypes: TASK_TYPES,
    availableSubjects: subjects
  };
}
