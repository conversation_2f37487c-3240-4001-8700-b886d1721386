import { Theme, ColorScheme } from '@/types/theme';

// Light theme colors (existing)
const lightColors: ColorScheme = {
  primary: '#7C3AED', // Primary color for backward compatibility
  background: {
    primary: '#F8FAFC',
    secondary: '#FFFFFF',
    tertiary: '#F1F5F9',
    card: '#FFFFFF',
    modal: '#FFFFFF',
  },
  text: {
    primary: '#1F2937',
    secondary: '#6B7280',
    tertiary: '#9CA3AF',
    disabled: '#D1D5DB',
    inverse: '#FFFFFF',
  },
  accent: {
    primary: '#7C3AED', // Darker purple for better contrast in light mode
    secondary: '#6366F1',
    hover: '#6D28D9', // Darker hover state
  },
  status: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  // Direct status colors for backward compatibility
  success: {
    primary: '#10B981',
  },
  warning: {
    primary: '#F59E0B',
  },
  error: {
    primary: '#EF4444',
  },
  // Border colors for backward compatibility
  border: {
    primary: '#E5E7EB',
  },
  interactive: {
    button: {
      background: '#7C3AED', // Darker purple for better contrast
      text: '#FFFFFF',
      hover: '#6D28D9', // Darker hover
      disabled: '#E5E7EB',
      pressed: '#5B21B6', // Even darker pressed state
    },
    input: {
      background: '#F9FAFB',
      border: '#E5E7EB',
      focusBorder: '#7C3AED', // Darker purple for focus
      placeholder: '#9CA3AF',
      errorBorder: '#EF4444',
    },
    link: {
      default: '#7C3AED', // Darker purple for links
      hover: '#6D28D9',
      pressed: '#5B21B6',
    },
    card: {
      background: '#FFFFFF',
      border: '#E5E7EB',
      hover: '#F8FAFC',
      pressed: '#F1F5F9',
    },
  },
  ui: {
    border: '#E5E7EB',
    shadow: '#000000',
    overlay: 'rgba(0, 0, 0, 0.5)',
    tabBar: {
      background: '#FFFFFF',
      border: '#E5E7EB',
      active: '#7C3AED', // Darker purple for active tab
      inactive: '#9CA3AF',
    },
    progressBarTrack: '#E5E7EB',
    progressBarFill: '#7C3AED', // Darker purple for progress
  },
  gradients: {
    primary: ['#7C3AED', '#5B21B6'], // Darker purple gradient
    secondary: ['#6366F1', '#4F46E5'], // Better contrast gradient
    accent: ['#10B981', '#059669'], // Improved green gradient
  },
};

// Dark theme colors (new)
const darkColors: ColorScheme = {
  primary: '#BB86FC', // Primary color for backward compatibility
  background: {
    primary: '#121212', // Deep charcoal as specified
    secondary: '#1E1E1E', // Rich dark gray as specified
    tertiary: '#2A2A2A', // Slightly lighter for input fields
    card: '#1E1E1E',
    modal: '#1E1E1E',
  },
  text: {
    primary: '#FFFFFF', // Pure white as specified
    secondary: '#B3B3B3', // Light gray as specified
    tertiary: '#808080', // Dark gray as specified
    disabled: '#666666', // Darker gray for disabled state
    inverse: '#121212',
  },
  accent: {
    primary: '#BB86FC', // Deep purple as specified
    secondary: '#C4A7FF', // Better purple tone for hover
    hover: '#C4A7FF',
  },
  status: {
    success: '#4ADE80',
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',
  },
  // Direct status colors for backward compatibility
  success: {
    primary: '#4ADE80',
  },
  warning: {
    primary: '#FBBF24',
  },
  error: {
    primary: '#F87171',
  },
  // Border colors for backward compatibility
  border: {
    primary: '#404040',
  },
  interactive: {
    button: {
      background: '#BB86FC',
      text: '#FFFFFF',
      hover: '#C4A7FF', // Better purple tone for hover
      disabled: '#404040',
      pressed: '#A855F7',
    },
    input: {
      background: '#2A2A2A', // Slightly lighter background as specified
      border: '#404040',
      focusBorder: '#BB86FC', // Accent-colored borders
      placeholder: '#808080',
      errorBorder: '#EF4444',
    },
    link: {
      default: '#BB86FC',
      hover: '#C4A7FF',
      pressed: '#A855F7',
    },
    card: {
      background: '#1E1E1E',
      border: '#404040',
      hover: '#2A2A2A',
      pressed: '#333333',
    },
  },
  ui: {
    border: '#404040',
    shadow: '#000000',
    overlay: 'rgba(0, 0, 0, 0.6)', // Semi-transparent overlay as specified
    tabBar: {
      background: '#1E1E1E',
      border: '#404040',
      active: '#BB86FC',
      inactive: '#808080',
    },
    progressBarTrack: '#404040',
    progressBarFill: '#BB86FC',
  },
  gradients: {
    primary: ['#BB86FC', '#9F67FF'], // Better purple gradient without white tones
    secondary: ['#8B5CF6', '#7C3AED'], // Improved secondary gradient
    accent: ['#4ADE80', '#34D399'],
  },
};

// Common spacing, border radius, shadows, and transitions
const commonThemeProperties = {
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    full: 9999,
  },
  transitions: {
    fast: 150,
    normal: 200, // 0.2s as specified
    slow: 300,
  },
};

// Light theme
export const lightTheme: Theme = {
  mode: 'light',
  colors: lightColors,
  ...commonThemeProperties,
  shadows: {
    sm: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.15,
      shadowRadius: 16,
      elevation: 8,
    },
  },
};

// Dark theme
export const darkTheme: Theme = {
  mode: 'dark',
  colors: darkColors,
  ...commonThemeProperties,
  shadows: {
    sm: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.3, // Higher opacity for dark shadows
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.4, // Subtle elevation with dark shadows
      shadowRadius: 4,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.5,
      shadowRadius: 16,
      elevation: 8,
    },
  },
};

export const themes = {
  light: lightTheme,
  dark: darkTheme,
};
