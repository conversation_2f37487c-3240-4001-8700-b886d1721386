// Shared color palette for subjects across all components
// This ensures consistent colors between Timer, Analytics, and other components
export const SUBJECT_COLORS = [
  "#4f46e5", // Indigo
  "#ec4899", // Pink
  "#06b6d4", // <PERSON><PERSON>
  "#f97316", // Orange
  "#10b981", // Emerald
  "#8b5cf6", // Violet
  "#f43f5e", // Rose
  "#0ea5e9", // Sky
  "#84cc16", // Lime
  "#14b8a6", // Teal
  "#d946ef", // Fuchsia
  "#f59e0b", // Amber
  "#6366f1", // Blue
  "#ef4444", // Red
  "#22c55e", // Green
  "#a855f7", // Purple
  "#3b82f6", // Blue-500
  "#eab308", // Yellow
];

// Additional Material 3 inspired colors
export const MATERIAL_COLORS = [
  "#6750A4", // Primary Purple
  "#625B71", // Primary Variant
  "#7D5260", // Secondary
  "#7C5800", // Tertiary
  "#BA1A1A", // Error
  "#006C4C", // Success
  "#1D192B", // Surface Variant
  "#49454F", // Outline
];

// Combined palette for maximum variety
export const ALL_SUBJECT_COLORS = [...SUBJECT_COLORS, ...MATERIAL_COLORS];

// Helper function to get a color by index (with wraparound)
export const getSubjectColor = (index: number): string => {
  return SUBJECT_COLORS[index % SUBJECT_COLORS.length];
};

// Helper function to get a random color from the palette
export const getRandomSubjectColor = (): string => {
  return SUBJECT_COLORS[Math.floor(Math.random() * SUBJECT_COLORS.length)];
};

// Helper function to get a random color from all available colors
export const getRandomColor = (): string => {
  return ALL_SUBJECT_COLORS[Math.floor(Math.random() * ALL_SUBJECT_COLORS.length)];
};

// Helper function to validate if a color is a valid hex color
export const isValidHexColor = (color: string): boolean => {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexColorRegex.test(color);
};

// Helper function to get contrasting text color for a background color
export const getContrastingTextColor = (backgroundColor: string): string => {
  // Remove # if present
  const hex = backgroundColor.replace('#', '');
  
  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Return black or white based on luminance
  return luminance > 0.5 ? '#000000' : '#FFFFFF';
};

// Helper function to lighten a color
export const lightenColor = (color: string, amount: number): string => {
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const amt = Math.round(2.55 * amount);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  
  return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255))
    .toString(16)
    .slice(1);
};

// Helper function to darken a color
export const darkenColor = (color: string, amount: number): string => {
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const amt = Math.round(2.55 * amount);
  const R = (num >> 16) - amt;
  const G = (num >> 8 & 0x00FF) - amt;
  const B = (num & 0x0000FF) - amt;
  
  return '#' + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
    (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
    (B > 255 ? 255 : B < 0 ? 0 : B))
    .toString(16)
    .slice(1);
};

// Color categories for better organization
export const COLOR_CATEGORIES = {
  BLUES: ["#4f46e5", "#06b6d4", "#0ea5e9", "#3b82f6"],
  PURPLES: ["#8b5cf6", "#d946ef", "#a855f7", "#6750A4"],
  PINKS: ["#ec4899", "#f43f5e"],
  GREENS: ["#10b981", "#84cc16", "#22c55e", "#006C4C"],
  ORANGES: ["#f97316", "#f59e0b", "#eab308"],
  TEALS: ["#14b8a6"],
  REDS: ["#ef4444", "#BA1A1A"],
  GRAYS: ["#625B71", "#7D5260", "#49454F"],
};

// Get colors by category
export const getColorsByCategory = (category: keyof typeof COLOR_CATEGORIES): string[] => {
  return COLOR_CATEGORIES[category] || [];
};
