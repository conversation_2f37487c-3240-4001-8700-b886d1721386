{"expo": {"name": "IsotopeAI", "slug": "isotope-ai-time-management", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "owner": "isotopeai", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.isotopeai.app", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSFamilyControlsUsageDescription": "This app uses Screen Time controls to help you stay focused by blocking distracting apps during your study sessions.", "NSUserTrackingUsageDescription": "This app tracks app usage to provide focus insights and block distracting apps during study sessions."}, "entitlements": {"com.apple.developer.family-controls": true}}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser", ["react-native-device-activity", {"appGroup": "group.com.isotopeai.app.deviceactivity"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "14fc8a2e-76bb-4897-8bab-4e392ede6140"}}, "android": {"package": "com.isotopeai.app"}}}