import { 
  TimerSession, 
  DailyStat, 
  WeeklyStat, 
  MonthlyStat, 
  SubjectStat, 
  TaskTypeStat, 
  Analytics,
  StreakInfo,
  DateRange,
  ChartDataPoint,
  TimeSeriesDataPoint,
  ApiResponse
} from '@/types/app';
import { enhancedSupabaseService } from './enhancedSupabaseService';

export class AnalyticsService {
  
  // Date utility functions
  private getDateString(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  private getWeekStart(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day;
    return new Date(d.setDate(diff));
  }

  private getMonthStart(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth(), 1);
  }

  private addDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  private addWeeks(date: Date, weeks: number): Date {
    return this.addDays(date, weeks * 7);
  }

  private addMonths(date: Date, months: number): Date {
    const result = new Date(date);
    result.setMonth(result.getMonth() + months);
    return result;
  }

  // Weekly Statistics Calculation
  async calculateWeeklyStats(userId: string, dateRange: DateRange): Promise<ApiResponse<WeeklyStat[]>> {
    try {
      const sessionsResponse = await enhancedSupabaseService.getStudySessions(userId, dateRange);
      if (!sessionsResponse.success || !sessionsResponse.data) {
        return { success: false, error: sessionsResponse.error };
      }

      const sessions = sessionsResponse.data;
      const weeklyStatsMap = new Map<string, WeeklyStat>();

      // Initialize weekly stats
      let currentWeekStart = this.getWeekStart(dateRange.start);
      while (currentWeekStart <= dateRange.end) {
        const weekEnd = this.addDays(currentWeekStart, 6);
        const weekKey = this.getDateString(currentWeekStart);
        
        weeklyStatsMap.set(weekKey, {
          weekStart: this.getDateString(currentWeekStart),
          weekEnd: this.getDateString(weekEnd),
          totalDuration: 0,
          sessionCount: 0,
          completedPomodoros: 0,
          dailyAverage: 0,
          subjectDurations: {},
          taskTypeDurations: {},
          targetAchievementRate: 0,
          targetAchieved: false,
          activeDays: 0,
          averageProductivityRating: 0
        });

        currentWeekStart = this.addWeeks(currentWeekStart, 1);
      }

      // Process sessions
      sessions.forEach(session => {
        const sessionDate = new Date(session.startTime);
        const weekStart = this.getWeekStart(sessionDate);
        const weekKey = this.getDateString(weekStart);
        const stat = weeklyStatsMap.get(weekKey);
        
        if (!stat) return;

        stat.totalDuration += session.duration;
        stat.sessionCount += 1;

        if (session.mode === 'pomodoro' && session.completed) {
          stat.completedPomodoros += 1;
        }

        if (session.subject) {
          stat.subjectDurations[session.subject] = (stat.subjectDurations[session.subject] || 0) + session.duration;
        }

        if (session.taskType) {
          stat.taskTypeDurations[session.taskType] = (stat.taskTypeDurations[session.taskType] || 0) + session.duration;
        }
      });

      // Calculate averages
      Array.from(weeklyStatsMap.values()).forEach(stat => {
        stat.dailyAverage = stat.totalDuration / 7; // 7 days in a week
      });

      return { success: true, data: Array.from(weeklyStatsMap.values()) };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: 'ANALYTICS_ERROR', 
          message: 'Failed to calculate weekly stats', 
          details: error, 
          timestamp: new Date() 
        } 
      };
    }
  }

  // Monthly Statistics Calculation
  async calculateMonthlyStats(userId: string, dateRange: DateRange): Promise<ApiResponse<MonthlyStat[]>> {
    try {
      const sessionsResponse = await enhancedSupabaseService.getStudySessions(userId, dateRange);
      if (!sessionsResponse.success || !sessionsResponse.data) {
        return { success: false, error: sessionsResponse.error };
      }

      const sessions = sessionsResponse.data;
      const monthlyStatsMap = new Map<string, MonthlyStat>();

      // Initialize monthly stats
      let currentMonthStart = this.getMonthStart(dateRange.start);
      while (currentMonthStart <= dateRange.end) {
        const monthKey = `${currentMonthStart.getFullYear()}-${String(currentMonthStart.getMonth() + 1).padStart(2, '0')}`;
        
        monthlyStatsMap.set(monthKey, {
          month: monthKey,
          year: currentMonthStart.getFullYear(),
          totalDuration: 0,
          sessionCount: 0,
          completedPomodoros: 0,
          dailyAverage: 0,
          weeklyAverage: 0,
          subjectDurations: {},
          taskTypeDurations: {},
          targetAchievementRate: 0,
          streakDays: 0,
          targetAchieved: false,
          activeDays: 0,
          averageProductivityRating: 0
        });

        currentMonthStart = this.addMonths(currentMonthStart, 1);
      }

      // Process sessions
      sessions.forEach(session => {
        const sessionDate = new Date(session.startTime);
        const monthKey = `${sessionDate.getFullYear()}-${String(sessionDate.getMonth() + 1).padStart(2, '0')}`;
        const stat = monthlyStatsMap.get(monthKey);
        
        if (!stat) return;

        stat.totalDuration += session.duration;
        stat.sessionCount += 1;

        if (session.mode === 'pomodoro' && session.completed) {
          stat.completedPomodoros += 1;
        }

        if (session.subject) {
          stat.subjectDurations[session.subject] = (stat.subjectDurations[session.subject] || 0) + session.duration;
        }

        if (session.taskType) {
          stat.taskTypeDurations[session.taskType] = (stat.taskTypeDurations[session.taskType] || 0) + session.duration;
        }
      });

      // Calculate averages
      Array.from(monthlyStatsMap.values()).forEach(stat => {
        const daysInMonth = new Date(parseInt(stat.month.split('-')[0]), parseInt(stat.month.split('-')[1]), 0).getDate();
        stat.dailyAverage = stat.totalDuration / daysInMonth;
        stat.weeklyAverage = stat.totalDuration / (daysInMonth / 7);
      });

      return { success: true, data: Array.from(monthlyStatsMap.values()) };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: 'ANALYTICS_ERROR', 
          message: 'Failed to calculate monthly stats', 
          details: error, 
          timestamp: new Date() 
        } 
      };
    }
  }

  // Subject Statistics Calculation
  async calculateSubjectStats(userId: string, dateRange: DateRange): Promise<ApiResponse<SubjectStat[]>> {
    try {
      const sessionsResponse = await enhancedSupabaseService.getStudySessions(userId, dateRange);
      if (!sessionsResponse.success || !sessionsResponse.data) {
        return { success: false, error: sessionsResponse.error };
      }

      const sessions = sessionsResponse.data;
      const subjectStatsMap = new Map<string, SubjectStat>();

      // Process sessions
      sessions.forEach(session => {
        if (!session.subject) return;

        let stat = subjectStatsMap.get(session.subject);
        if (!stat) {
          stat = {
            subject: session.subject,
            color: session.subjectColor || '#3B82F6',
            totalDuration: 0,
            sessionCount: 0,
            averageSessionDuration: 0,
            averageProductivityRating: 0,
            lastStudied: session.date,
            streakDays: 0
          };
          subjectStatsMap.set(session.subject, stat);
        }

        stat.totalDuration += session.duration;
        stat.sessionCount += 1;
        
        // Update last studied date
        if (session.date > stat.lastStudied) {
          stat.lastStudied = session.date;
        }
      });

      // Calculate averages
      Array.from(subjectStatsMap.values()).forEach(stat => {
        stat.averageSessionDuration = stat.sessionCount > 0 ? stat.totalDuration / stat.sessionCount : 0;
        
        // Calculate average productivity rating
        const subjectSessions = sessions.filter(s => s.subject === stat.subject && s.productivityRating);
        if (subjectSessions.length > 0) {
          const totalRating = subjectSessions.reduce((sum, s) => sum + (s.productivityRating || 0), 0);
          stat.averageProductivityRating = totalRating / subjectSessions.length;
        }
      });

      return { success: true, data: Array.from(subjectStatsMap.values()) };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: 'ANALYTICS_ERROR', 
          message: 'Failed to calculate subject stats', 
          details: error, 
          timestamp: new Date() 
        } 
      };
    }
  }

  // Task Type Statistics Calculation
  async calculateTaskTypeStats(userId: string, dateRange: DateRange): Promise<ApiResponse<TaskTypeStat[]>> {
    try {
      const sessionsResponse = await enhancedSupabaseService.getStudySessions(userId, dateRange);
      if (!sessionsResponse.success || !sessionsResponse.data) {
        return { success: false, error: sessionsResponse.error };
      }

      const sessions = sessionsResponse.data;
      const taskTypeStatsMap = new Map<string, TaskTypeStat>();
      const totalDuration = sessions.reduce((sum, session) => sum + session.duration, 0);

      // Process sessions
      sessions.forEach(session => {
        if (!session.taskType) return;

        let stat = taskTypeStatsMap.get(session.taskType);
        if (!stat) {
          stat = {
            taskType: session.taskType,
            totalDuration: 0,
            sessionCount: 0,
            averageSessionDuration: 0,
            averageProductivityRating: 0,
            percentage: 0
          };
          taskTypeStatsMap.set(session.taskType, stat);
        }

        stat.totalDuration += session.duration;
        stat.sessionCount += 1;
      });

      // Calculate averages and percentages
      Array.from(taskTypeStatsMap.values()).forEach(stat => {
        stat.averageSessionDuration = stat.sessionCount > 0 ? stat.totalDuration / stat.sessionCount : 0;
        stat.percentage = totalDuration > 0 ? (stat.totalDuration / totalDuration) * 100 : 0;
        
        // Calculate average productivity rating
        const taskTypeSessions = sessions.filter(s => s.taskType === stat.taskType && s.productivityRating);
        if (taskTypeSessions.length > 0) {
          const totalRating = taskTypeSessions.reduce((sum, s) => sum + (s.productivityRating || 0), 0);
          stat.averageProductivityRating = totalRating / taskTypeSessions.length;
        }
      });

      return { success: true, data: Array.from(taskTypeStatsMap.values()) };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: 'ANALYTICS_ERROR', 
          message: 'Failed to calculate task type stats', 
          details: error, 
          timestamp: new Date() 
        } 
      };
    }
  }

  // Comprehensive Analytics Calculation
  async calculateCompleteAnalytics(userId: string, dateRange: DateRange): Promise<ApiResponse<Analytics>> {
    try {
      // Fetch all required data in parallel
      const [
        dailyStatsResponse,
        weeklyStatsResponse,
        monthlyStatsResponse,
        subjectStatsResponse,
        taskTypeStatsResponse,
        streakResponse
      ] = await Promise.all([
        enhancedSupabaseService.getDailyStats(userId, dateRange),
        this.calculateWeeklyStats(userId, dateRange),
        this.calculateMonthlyStats(userId, dateRange),
        this.calculateSubjectStats(userId, dateRange),
        this.calculateTaskTypeStats(userId, dateRange),
        enhancedSupabaseService.getStreakInfo(userId)
      ]);

      // Check for errors
      if (!dailyStatsResponse.success) return { success: false, error: dailyStatsResponse.error };
      if (!weeklyStatsResponse.success) return { success: false, error: weeklyStatsResponse.error };
      if (!monthlyStatsResponse.success) return { success: false, error: monthlyStatsResponse.error };
      if (!subjectStatsResponse.success) return { success: false, error: subjectStatsResponse.error };
      if (!taskTypeStatsResponse.success) return { success: false, error: taskTypeStatsResponse.error };
      if (!streakResponse.success) return { success: false, error: streakResponse.error };

      const analytics: Analytics = {
        dailyStats: dailyStatsResponse.data!,
        weeklyStats: weeklyStatsResponse.data!,
        monthlyStats: monthlyStatsResponse.data!,
        subjectStats: subjectStatsResponse.data!,
        taskTypeStats: taskTypeStatsResponse.data!,
        streakInfo: streakResponse.data!,
        dateRange,
        generatedAt: new Date()
      };

      return { success: true, data: analytics };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: 'Failed to calculate complete analytics',
          details: error,
          timestamp: new Date()
        }
      };
    }
  }

  // Chart Data Generation
  async generateTimeSeriesData(userId: string, dateRange: DateRange, granularity: 'daily' | 'weekly' | 'monthly'): Promise<ApiResponse<TimeSeriesDataPoint[]>> {
    try {
      const sessionsResponse = await enhancedSupabaseService.getStudySessions(userId, dateRange);
      if (!sessionsResponse.success || !sessionsResponse.data) {
        return { success: false, error: sessionsResponse.error };
      }

      const sessions = sessionsResponse.data;
      const dataPoints: TimeSeriesDataPoint[] = [];

      if (granularity === 'daily') {
        const dailyData = new Map<string, number>();

        // Initialize all days in range
        let currentDate = new Date(dateRange.start);
        while (currentDate <= dateRange.end) {
          const dateStr = this.getDateString(currentDate);
          dailyData.set(dateStr, 0);
          currentDate = this.addDays(currentDate, 1);
        }

        // Aggregate session data
        sessions.forEach(session => {
          const dateStr = session.date;
          const current = dailyData.get(dateStr) || 0;
          dailyData.set(dateStr, current + session.duration);
        });

        // Convert to chart data
        Array.from(dailyData.entries()).forEach(([date, duration]) => {
          dataPoints.push({
            date,
            value: Math.round(duration / 60), // Convert to minutes
            label: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
          });
        });
      }

      return { success: true, data: dataPoints };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: 'Failed to generate time series data',
          details: error,
          timestamp: new Date()
        }
      };
    }
  }

  async generateSubjectChartData(userId: string, dateRange: DateRange): Promise<ApiResponse<ChartDataPoint[]>> {
    try {
      const subjectStatsResponse = await this.calculateSubjectStats(userId, dateRange);
      if (!subjectStatsResponse.success || !subjectStatsResponse.data) {
        return { success: false, error: subjectStatsResponse.error };
      }

      const chartData: ChartDataPoint[] = subjectStatsResponse.data.map(stat => ({
        label: stat.subject,
        value: Math.round(stat.totalDuration / 60), // Convert to minutes
        color: stat.color,
        percentage: 0 // Will be calculated below
      }));

      // Calculate percentages
      const totalDuration = chartData.reduce((sum, point) => sum + point.value, 0);
      chartData.forEach(point => {
        point.percentage = totalDuration > 0 ? (point.value / totalDuration) * 100 : 0;
      });

      return { success: true, data: chartData };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: 'Failed to generate subject chart data',
          details: error,
          timestamp: new Date()
        }
      };
    }
  }

  async generateTaskTypeChartData(userId: string, dateRange: DateRange): Promise<ApiResponse<ChartDataPoint[]>> {
    try {
      const taskTypeStatsResponse = await this.calculateTaskTypeStats(userId, dateRange);
      if (!taskTypeStatsResponse.success || !taskTypeStatsResponse.data) {
        return { success: false, error: taskTypeStatsResponse.error };
      }

      const colors = ['#6366F1', '#8B5CF6', '#EC4899', '#EF4444', '#F59E0B', '#10B981', '#06B6D4', '#84CC16'];

      const chartData: ChartDataPoint[] = taskTypeStatsResponse.data.map((stat, index) => ({
        label: stat.taskType,
        value: Math.round(stat.totalDuration / 60), // Convert to minutes
        color: colors[index % colors.length],
        percentage: stat.percentage
      }));

      return { success: true, data: chartData };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: 'Failed to generate task type chart data',
          details: error,
          timestamp: new Date()
        }
      };
    }
  }

  // Performance Metrics
  async calculatePerformanceMetrics(userId: string, dateRange: DateRange): Promise<ApiResponse<{
    totalStudyTime: number;
    averageSessionDuration: number;
    totalSessions: number;
    completedPomodoros: number;
    averageProductivityRating: number;
    targetAchievementRate: number;
    mostProductiveDay: string;
    mostStudiedSubject: string;
    consistencyScore: number;
  }>> {
    try {
      const sessionsResponse = await enhancedSupabaseService.getStudySessions(userId, dateRange);
      if (!sessionsResponse.success || !sessionsResponse.data) {
        return { success: false, error: sessionsResponse.error };
      }

      const sessions = sessionsResponse.data;
      const totalStudyTime = sessions.reduce((sum, session) => sum + session.duration, 0);
      const totalSessions = sessions.length;
      const completedPomodoros = sessions.filter(s => s.mode === 'pomodoro' && s.completed).length;

      // Calculate average productivity rating
      const ratedSessions = sessions.filter(s => s.productivityRating);
      const averageProductivityRating = ratedSessions.length > 0
        ? ratedSessions.reduce((sum, s) => sum + (s.productivityRating || 0), 0) / ratedSessions.length
        : 0;

      // Find most productive day
      const dailyTotals = new Map<string, number>();
      sessions.forEach(session => {
        const date = session.date;
        dailyTotals.set(date, (dailyTotals.get(date) || 0) + session.duration);
      });

      const mostProductiveDay = Array.from(dailyTotals.entries())
        .sort(([,a], [,b]) => b - a)[0]?.[0] || '';

      // Find most studied subject
      const subjectTotals = new Map<string, number>();
      sessions.forEach(session => {
        if (session.subject) {
          subjectTotals.set(session.subject, (subjectTotals.get(session.subject) || 0) + session.duration);
        }
      });

      const mostStudiedSubject = Array.from(subjectTotals.entries())
        .sort(([,a], [,b]) => b - a)[0]?.[0] || '';

      // Calculate consistency score (based on how many days had study sessions)
      const studyDays = new Set(sessions.map(s => s.date)).size;
      const totalDays = Math.ceil((dateRange.end.getTime() - dateRange.start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      const consistencyScore = totalDays > 0 ? (studyDays / totalDays) * 100 : 0;

      const metrics = {
        totalStudyTime,
        averageSessionDuration: totalSessions > 0 ? totalStudyTime / totalSessions : 0,
        totalSessions,
        completedPomodoros,
        averageProductivityRating,
        targetAchievementRate: 0, // Would need user's daily target to calculate
        mostProductiveDay,
        mostStudiedSubject,
        consistencyScore
      };

      return { success: true, data: metrics };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: 'Failed to calculate performance metrics',
          details: error,
          timestamp: new Date()
        }
      };
    }
  }

  // Utility methods for date calculations
  private getDateRange(period: 'week' | 'month' | 'year', referenceDate: Date = new Date()): DateRange {
    const end = new Date(referenceDate);
    let start: Date;

    switch (period) {
      case 'week':
        start = this.getWeekStart(end);
        break;
      case 'month':
        start = this.getMonthStart(end);
        break;
      case 'year':
        start = new Date(end.getFullYear(), 0, 1);
        break;
    }

    return { start, end };
  }

  // Quick analytics methods for dashboard
  async getQuickStats(userId: string): Promise<ApiResponse<{
    todayTime: number;
    weekTime: number;
    monthTime: number;
    currentStreak: number;
    totalSessions: number;
  }>> {
    try {
      const today = new Date();
      const weekRange = this.getDateRange('week', today);
      const monthRange = this.getDateRange('month', today);

      const [todayResponse, weekResponse, monthResponse, streakResponse, totalResponse] = await Promise.all([
        enhancedSupabaseService.getStudySessions(userId, {
          start: new Date(today.getFullYear(), today.getMonth(), today.getDate()),
          end: today
        }),
        enhancedSupabaseService.getStudySessions(userId, weekRange),
        enhancedSupabaseService.getStudySessions(userId, monthRange),
        enhancedSupabaseService.getStreakInfo(userId),
        enhancedSupabaseService.getSessionCount(userId)
      ]);

      const todayTime = todayResponse.success ?
        todayResponse.data!.reduce((sum, s) => sum + s.duration, 0) : 0;
      const weekTime = weekResponse.success ?
        weekResponse.data!.reduce((sum, s) => sum + s.duration, 0) : 0;
      const monthTime = monthResponse.success ?
        monthResponse.data!.reduce((sum, s) => sum + s.duration, 0) : 0;
      const currentStreak = streakResponse.success ? streakResponse.data!.currentStreak : 0;
      const totalSessions = totalResponse.success ? totalResponse.data! : 0;

      return {
        success: true,
        data: {
          todayTime,
          weekTime,
          monthTime,
          currentStreak,
          totalSessions
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: 'Failed to get quick stats',
          details: error,
          timestamp: new Date()
        }
      };
    }
  }
}

export const analyticsService = new AnalyticsService();
