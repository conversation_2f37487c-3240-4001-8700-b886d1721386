import { supabase } from '@/lib/supabase';
import { TimerSession, Subject } from '@/types/app';

export class SupabaseService {
  // Study Sessions
  async getStudySessions(userId: string): Promise<TimerSession[]> {
    try {
      const { data, error } = await supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return (data || []).map((session: any) => ({
        id: session.id,
        startTime: new Date(session.start_time),
        endTime: session.end_time ? new Date(session.end_time) : null,
        duration: session.duration,
        subject: session.subject || undefined,
        subjectColor: session.subject_color || undefined,
        mode: session.mode as 'stopwatch' | 'pomodoro' || 'stopwatch',
        phase: session.phase as 'work' | 'break' || undefined,
        completed: session.completed || false,
        notes: session.notes || undefined,
        taskName: session.task_name || undefined,
        taskType: session.task_type || undefined,
        productivityRating: session.productivity_rating || undefined,
        feedback: session.feedback || undefined,
        date: session.date,
      }));
    } catch (error) {
      console.error('Error fetching study sessions:', error);
      return [];
    }
  }

  async createStudySession(userId: string, session: Omit<TimerSession, 'id'>): Promise<TimerSession | null> {
    try {
      const { data, error } = await supabase
        .from('study_sessions')
        .insert({
          user_id: userId,
          start_time: session.startTime.toISOString(),
          end_time: session.endTime?.toISOString() || null,
          duration: session.duration,
          subject: session.subject || null,
          subject_color: session.subjectColor || null,
          mode: session.mode,
          phase: session.phase || null,
          completed: session.completed,
          notes: session.notes || null,
          task_name: session.taskName || null,
          task_type: session.taskType || null,
          productivity_rating: session.productivityRating || null,
          feedback: session.feedback || null,
          date: session.date,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        startTime: new Date(data.start_time),
        endTime: data.end_time ? new Date(data.end_time) : null,
        duration: data.duration,
        subject: data.subject || undefined,
        subjectColor: data.subject_color || undefined,
        mode: data.mode as 'stopwatch' | 'pomodoro',
        phase: data.phase as 'work' | 'break' || undefined,
        completed: data.completed,
        notes: data.notes || undefined,
        taskName: data.task_name || undefined,
        taskType: data.task_type || undefined,
        productivityRating: data.productivity_rating || undefined,
        feedback: data.feedback || undefined,
        date: data.date,
      };
    } catch (error) {
      console.error('Error creating study session:', error);
      return null;
    }
  }

  async updateStudySession(sessionId: string, updates: Partial<TimerSession>): Promise<boolean> {
    try {
      const updateData: any = {};
      
      if (updates.endTime !== undefined) updateData.end_time = updates.endTime?.toISOString() || null;
      if (updates.duration !== undefined) updateData.duration = updates.duration;
      if (updates.completed !== undefined) updateData.completed = updates.completed;
      if (updates.notes !== undefined) updateData.notes = updates.notes || null;
      if (updates.productivityRating !== undefined) updateData.productivity_rating = updates.productivityRating || null;
      if (updates.feedback !== undefined) updateData.feedback = updates.feedback || null;

      const { error } = await supabase
        .from('study_sessions')
        .update(updateData)
        .eq('id', sessionId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating study session:', error);
      return false;
    }
  }

  // User Subjects
  async getUserSubjects(userId: string): Promise<Subject[]> {
    try {
      const { data, error } = await supabase
        .from('userSubjects')
        .select('*')
        .eq('userId', userId)
        .order('createdAt', { ascending: false });

      if (error) throw error;

      return (data || []).map((subject: any) => ({
        id: subject.id,
        name: subject.name,
        color: subject.color || '#6B46C1',
        createdAt: new Date(subject.createdAt || new Date()),
      }));
    } catch (error) {
      console.error('Error fetching user subjects:', error);
      return [];
    }
  }

  async createUserSubject(userId: string, subject: Omit<Subject, 'id'>): Promise<Subject | null> {
    try {
      const { data, error } = await supabase
        .from('userSubjects')
        .insert({
          userId,
          name: subject.name,
          color: subject.color,
          createdAt: subject.createdAt.toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        name: data.name,
        color: data.color || '#6B46C1',
        createdAt: new Date(data.createdAt || new Date()),
      };
    } catch (error) {
      console.error('Error creating user subject:', error);
      return null;
    }
  }

  async updateUserSubject(subjectId: string, updates: Partial<Subject>): Promise<boolean> {
    try {
      const updateData: any = {};
      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.color !== undefined) updateData.color = updates.color;

      const { error } = await supabase
        .from('userSubjects')
        .update(updateData)
        .eq('id', subjectId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating user subject:', error);
      return false;
    }
  }

  async deleteUserSubject(subjectId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('userSubjects')
        .delete()
        .eq('id', subjectId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting user subject:', error);
      return false;
    }
  }

  // User Profile
  async getUserProfile(userId: string) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  }

  async createUserProfile(userId: string, profileData: any): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('users')
        .upsert({
          id: userId,
          ...profileData,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error creating user profile:', error);
      return false;
    }
  }

  async updateUserProfile(userId: string, updates: any): Promise<boolean> {
    try {
      // Use update instead of upsert to avoid creating new records without required fields
      const { error } = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating user profile:', error);
      return false;
    }
  }

  // Real-time subscriptions
  subscribeToStudySessions(userId: string, callback: (sessions: TimerSession[]) => void) {
    return supabase
      .channel('study_sessions')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'study_sessions',
          filter: `user_id=eq.${userId}`,
        },
        () => {
          // Refetch sessions when changes occur
          this.getStudySessions(userId).then(callback);
        }
      )
      .subscribe();
  }

  subscribeToUserSubjects(userId: string, callback: (subjects: Subject[]) => void) {
    return supabase
      .channel('user_subjects')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'userSubjects',
          filter: `userId=eq.${userId}`,
        },
        () => {
          // Refetch subjects when changes occur
          this.getUserSubjects(userId).then(callback);
        }
      )
      .subscribe();
  }
}

export const supabaseService = new SupabaseService();
