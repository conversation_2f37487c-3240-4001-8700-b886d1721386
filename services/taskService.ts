import { supabase } from '@/lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface Task {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'todo' | 'in_progress' | 'completed' | 'cancelled';
  due_date?: string;
  start_date?: string;
  completion_date?: string;
  estimated_duration?: number; // in minutes
  actual_duration?: number; // in minutes
  progress_percentage: number;
  subject_id?: string;
  tags: string[];
  is_milestone: boolean;
  parent_task_id?: string;
  order_index: number;
  reminder_enabled: boolean;
  reminder_time?: string;
  study_session_ids: string[];
  total_study_time: number; // in minutes
  notes?: string;
  attachments: any[];
  metadata: any;
  created_at: string;
  updated_at: string;
}

export interface TaskCategory {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  order_index: number;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface TaskMilestone {
  id: string;
  task_id: string;
  title: string;
  description?: string;
  target_percentage: number;
  achieved_at?: string;
  is_achieved: boolean;
  reward_points: number;
  created_at: string;
}

export interface CreateTaskData {
  title: string;
  description?: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  due_date?: Date;
  start_date?: Date;
  estimated_duration?: number;
  subject_id?: string;
  tags?: string[];
  is_milestone?: boolean;
  parent_task_id?: string;
  reminder_enabled?: boolean;
  reminder_time?: Date;
  notes?: string;
}

export interface UpdateTaskData {
  title?: string;
  description?: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  status?: 'todo' | 'in_progress' | 'completed' | 'cancelled';
  due_date?: Date | null;
  start_date?: Date | null;
  completion_date?: Date | null;
  estimated_duration?: number;
  actual_duration?: number;
  progress_percentage?: number;
  subject_id?: string;
  tags?: string[];
  is_milestone?: boolean;
  parent_task_id?: string;
  order_index?: number;
  reminder_enabled?: boolean;
  reminder_time?: Date | null;
  study_session_ids?: string[];
  total_study_time?: number;
  notes?: string;
  attachments?: any[];
  metadata?: any;
}

export interface TaskFilters {
  status?: string[];
  category?: string[];
  priority?: string[];
  due_date_from?: Date;
  due_date_to?: Date;
  search?: string;
  tags?: string[];
  is_milestone?: boolean;
  parent_task_id?: string;
}

export interface TaskStats {
  total_tasks: number;
  completed_tasks: number;
  in_progress_tasks: number;
  overdue_tasks: number;
  completion_rate: number;
  total_study_time: number;
  average_completion_time: number;
  tasks_by_category: { [key: string]: number };
  tasks_by_priority: { [key: string]: number };
}

class TaskService {
  private realtimeChannel: RealtimeChannel | null = null;

  // Initialize default categories for a user
  async initializeUserCategories(userId: string): Promise<void> {
    try {
      const { error } = await supabase.rpc('create_default_task_categories', {
        user_id_param: userId
      });
      
      if (error) {
        console.error('Error initializing user categories:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error in initializeUserCategories:', error);
      throw error;
    }
  }

  // Task CRUD Operations
  async createTask(userId: string, taskData: CreateTaskData): Promise<Task | null> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .insert({
          user_id: userId,
          title: taskData.title,
          description: taskData.description,
          category: taskData.category || 'general',
          priority: taskData.priority || 'medium',
          due_date: taskData.due_date?.toISOString(),
          start_date: taskData.start_date?.toISOString(),
          estimated_duration: taskData.estimated_duration,
          subject_id: taskData.subject_id,
          tags: taskData.tags || [],
          is_milestone: taskData.is_milestone || false,
          parent_task_id: taskData.parent_task_id,
          reminder_enabled: taskData.reminder_enabled || false,
          reminder_time: taskData.reminder_time?.toISOString(),
          notes: taskData.notes,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating task:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in createTask:', error);
      return null;
    }
  }

  async getTasks(userId: string, filters?: TaskFilters): Promise<Task[]> {
    try {
      let query = supabase
        .from('tasks')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters) {
        if (filters.status && filters.status.length > 0) {
          query = query.in('status', filters.status);
        }
        if (filters.category && filters.category.length > 0) {
          query = query.in('category', filters.category);
        }
        if (filters.priority && filters.priority.length > 0) {
          query = query.in('priority', filters.priority);
        }
        if (filters.due_date_from) {
          query = query.gte('due_date', filters.due_date_from.toISOString());
        }
        if (filters.due_date_to) {
          query = query.lte('due_date', filters.due_date_to.toISOString());
        }
        if (filters.is_milestone !== undefined) {
          query = query.eq('is_milestone', filters.is_milestone);
        }
        if (filters.parent_task_id) {
          query = query.eq('parent_task_id', filters.parent_task_id);
        }
        if (filters.search) {
          query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
        }
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching tasks:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getTasks:', error);
      return [];
    }
  }

  async getTaskById(taskId: string): Promise<Task | null> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (error) {
        console.error('Error fetching task:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getTaskById:', error);
      return null;
    }
  }

  async updateTask(taskId: string, updates: UpdateTaskData): Promise<Task | null> {
    try {
      const updateData: any = { ...updates };
      
      // Convert dates to ISO strings
      if (updates.due_date !== undefined) {
        updateData.due_date = updates.due_date?.toISOString() || null;
      }
      if (updates.start_date !== undefined) {
        updateData.start_date = updates.start_date?.toISOString() || null;
      }
      if (updates.completion_date !== undefined) {
        updateData.completion_date = updates.completion_date?.toISOString() || null;
      }
      if (updates.reminder_time !== undefined) {
        updateData.reminder_time = updates.reminder_time?.toISOString() || null;
      }

      const { data, error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', taskId)
        .select()
        .single();

      if (error) {
        console.error('Error updating task:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in updateTask:', error);
      return null;
    }
  }

  async deleteTask(taskId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId);

      if (error) {
        console.error('Error deleting task:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteTask:', error);
      return false;
    }
  }

  // Task completion and progress
  async completeTask(taskId: string): Promise<Task | null> {
    return this.updateTask(taskId, {
      status: 'completed',
      progress_percentage: 100,
      completion_date: new Date(),
    });
  }

  async updateTaskProgress(taskId: string, progressPercentage: number): Promise<Task | null> {
    const updates: UpdateTaskData = {
      progress_percentage: Math.max(0, Math.min(100, progressPercentage)),
    };

    // Auto-complete if progress reaches 100%
    if (progressPercentage >= 100) {
      updates.status = 'completed';
      updates.completion_date = new Date();
    }

    return this.updateTask(taskId, updates);
  }

  // Category management
  async getCategories(userId: string): Promise<TaskCategory[]> {
    try {
      const { data, error } = await supabase
        .from('task_categories')
        .select('*')
        .eq('user_id', userId)
        .order('order_index', { ascending: true });

      if (error) {
        console.error('Error fetching categories:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getCategories:', error);
      return [];
    }
  }

  async createCategory(userId: string, categoryData: Omit<TaskCategory, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<TaskCategory | null> {
    try {
      const { data, error } = await supabase
        .from('task_categories')
        .insert({
          user_id: userId,
          ...categoryData,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating category:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in createCategory:', error);
      return null;
    }
  }

  // Task statistics
  async getTaskStats(userId: string): Promise<TaskStats> {
    try {
      const tasks = await this.getTasks(userId);
      const now = new Date();

      const stats: TaskStats = {
        total_tasks: tasks.length,
        completed_tasks: tasks.filter(t => t.status === 'completed').length,
        in_progress_tasks: tasks.filter(t => t.status === 'in_progress').length,
        overdue_tasks: tasks.filter(t =>
          t.due_date &&
          new Date(t.due_date) < now &&
          t.status !== 'completed'
        ).length,
        completion_rate: 0,
        total_study_time: tasks.reduce((sum, t) => sum + t.total_study_time, 0),
        average_completion_time: 0,
        tasks_by_category: {},
        tasks_by_priority: {},
      };

      // Calculate completion rate
      if (stats.total_tasks > 0) {
        stats.completion_rate = (stats.completed_tasks / stats.total_tasks) * 100;
      }

      // Calculate average completion time
      const completedTasks = tasks.filter(t => t.status === 'completed' && t.actual_duration);
      if (completedTasks.length > 0) {
        stats.average_completion_time = completedTasks.reduce((sum, t) => sum + (t.actual_duration || 0), 0) / completedTasks.length;
      }

      // Group by category
      tasks.forEach(task => {
        stats.tasks_by_category[task.category] = (stats.tasks_by_category[task.category] || 0) + 1;
      });

      // Group by priority
      tasks.forEach(task => {
        stats.tasks_by_priority[task.priority] = (stats.tasks_by_priority[task.priority] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Error in getTaskStats:', error);
      return {
        total_tasks: 0,
        completed_tasks: 0,
        in_progress_tasks: 0,
        overdue_tasks: 0,
        completion_rate: 0,
        total_study_time: 0,
        average_completion_time: 0,
        tasks_by_category: {},
        tasks_by_priority: {},
      };
    }
  }

  // Real-time subscriptions
  subscribeToTasks(userId: string, callback: (payload: any) => void): RealtimeChannel {
    this.realtimeChannel = supabase
      .channel('tasks_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tasks',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();

    return this.realtimeChannel;
  }

  unsubscribeFromTasks(): void {
    if (this.realtimeChannel) {
      supabase.removeChannel(this.realtimeChannel);
      this.realtimeChannel = null;
    }
  }

  // Utility methods
  async getOverdueTasks(userId: string): Promise<Task[]> {
    const now = new Date();
    return this.getTasks(userId, {
      due_date_to: now,
      status: ['todo', 'in_progress'],
    });
  }

  async getUpcomingTasks(userId: string, days: number = 7): Promise<Task[]> {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(now.getDate() + days);

    return this.getTasks(userId, {
      due_date_from: now,
      due_date_to: futureDate,
      status: ['todo', 'in_progress'],
    });
  }

  async getTasksByCategory(userId: string, category: string): Promise<Task[]> {
    return this.getTasks(userId, { category: [category] });
  }

  async getSubtasks(parentTaskId: string): Promise<Task[]> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('parent_task_id', parentTaskId)
        .order('order_index', { ascending: true });

      if (error) {
        console.error('Error fetching subtasks:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getSubtasks:', error);
      return [];
    }
  }
}

export const taskService = new TaskService();
